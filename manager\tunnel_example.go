package manager

import (
	"log"

	"github.com/OpenIoTHub/server-go/config"
)

// 全局穿透管理器实例
var GlobalTunnelManager *TunnelManager
var GlobalTunnelAPI *TunnelAPI

// 确保穿透服务已初始化
func EnsureTunnelServiceInitialized() error {
	if GlobalTunnelManager == nil {
		// 初始化会话管理器
		InitSessionsCtl()
		// 初始化穿透服务
		InitTunnelService(&SessionsCtl)
	}
	return nil
}

// 初始化穿透服务
func InitTunnelService(sessionManager *SessionsManager) {
	// 创建穿透管理器
	GlobalTunnelManager = NewTunnelManager(sessionManager)

	// 创建API处理器
	// 从配置中获取API认证设置
	apiKey := config.ExtendedConfigMode.APIAuth.APIKey
	authEnabled := config.ExtendedConfigMode.APIAuth.Enabled
	GlobalTunnelAPI = NewTunnelAPI(GlobalTunnelManager, apiKey, authEnabled)

	// 加载配置
	err := GlobalTunnelManager.LoadAllConfigs()
	if err != nil {
		log.Printf("加载穿透配置失败: %s", err.Error())
	}

	log.Println("穿透服务初始化完成")
}

// 启动穿透API服务器（可选）
func StartTunnelAPIServer(port int) {
	if GlobalTunnelAPI == nil {
		log.Println("穿透API未初始化")
		return
	}

	go func() {
		err := GlobalTunnelAPI.StartAPIServer(port)
		if err != nil {
			log.Printf("穿透API服务器启动失败: %s", err.Error())
		}
	}()

	// 输出API认证状态
	if config.ExtendedConfigMode.APIAuth.Enabled {
		log.Printf("API认证已启用，请在请求头中添加 Authorization: Bearer %s\n", config.ExtendedConfigMode.APIAuth.APIKey)
	} else {
		log.Println("API认证已禁用")
	}
}

// 停止所有穿透服务
func StopAllTunnelServices() {
	if GlobalTunnelManager != nil {
		GlobalTunnelManager.StopAll()
	}
}

// ================ 使用示例 ================

// 示例：添加SSH穿透
func ExampleAddSSHTunnel() {
	if GlobalTunnelManager == nil {
		log.Println("穿透管理器未初始化")
		return
	}

	// 创建SSH穿透配置
	sshConfig := CreateSSHTunnel(
		2222,            // 监听端口
		"gateway-123",   // 网关ID
		"*************", // 内网SSH服务IP
		22,              // 内网SSH服务端口
		"user",          // SSH用户名
		"password",      // SSH密码
		"家里的树莓派SSH服务",   // 描述
	)

	// 添加穿透服务
	err := GlobalTunnelManager.AddTunnel(sshConfig)
	if err != nil {
		log.Printf("添加SSH穿透失败: %s", err.Error())
		return
	}

	log.Printf("SSH穿透添加成功: ssh -p 2222 user@your-server-ip")
}

// 示例：添加TCP穿透（HTTP服务）
func ExampleAddTCPTunnel() {
	if GlobalTunnelManager == nil {
		log.Println("穿透管理器未初始化")
		return
	}

	// 创建TCP穿透配置
	tcpConfig := CreateTCPTunnel(
		8080,            // 监听端口
		"gateway-123",   // 网关ID
		"*************", // 内网服务IP
		80,              // 内网服务端口
		"内网Web服务",       // 描述
	)

	// 添加穿透服务
	err := GlobalTunnelManager.AddTunnel(tcpConfig)
	if err != nil {
		log.Printf("添加TCP穿透失败: %s", err.Error())
		return
	}

	log.Printf("TCP穿透添加成功: http://your-server-ip:8080")
}

// 示例：添加WebSocket穿透
func ExampleAddWebSocketTunnel() {
	if GlobalTunnelManager == nil {
		log.Println("穿透管理器未初始化")
		return
	}

	// 创建WebSocket穿透配置
	wsConfig := CreateWebSocketTunnel(
		8081,                           // 监听端口
		"gateway-123",                  // 网关ID
		"*************",                // 内网服务IP
		8081,                           // 内网服务端口
		"chat",                         // 协议
		"http://example.com",           // Origin
		"ws://*************:8081/chat", // 目标URL
		"内网WebSocket聊天服务",              // 描述
	)

	// 添加穿透服务
	err := GlobalTunnelManager.AddTunnel(wsConfig)
	if err != nil {
		log.Printf("添加WebSocket穿透失败: %s", err.Error())
		return
	}

	log.Printf("WebSocket穿透添加成功: ws://your-server-ip:8081")
}

// 示例：添加串口穿透
func ExampleAddSerialPortTunnel() {
	if GlobalTunnelManager == nil {
		log.Println("穿透管理器未初始化")
		return
	}

	// 创建串口穿透配置
	serialConfig := CreateSerialPortTunnel(
		8082,          // 监听端口
		"gateway-123", // 网关ID
		"COM4",        // 串口名称
		"调试串口",        // 描述
		9600,          // 波特率
		8,             // 数据位
		1,             // 停止位
		4,             // 最小读取大小
	)

	// 添加穿透服务
	err := GlobalTunnelManager.AddTunnel(serialConfig)
	if err != nil {
		log.Printf("添加串口穿透失败: %s", err.Error())
		return
	}

	log.Printf("串口穿透添加成功: telnet your-server-ip 8082")
}

// 示例：批量添加穿透服务
func ExampleBatchAddTunnels() {
	if GlobalTunnelManager == nil {
		log.Println("穿透管理器未初始化")
		return
	}

	// 定义多个穿透配置
	tunnels := []*TunnelConfig{
		CreateTCPTunnel(8080, "gateway-123", "*************", 80, "内网Web服务"),
		CreateSSHTunnel(2222, "gateway-123", "*************", 22, "admin", "password", "SSH服务"),
		CreateTCPTunnel(3306, "gateway-123", "*************", 3306, "MySQL数据库"),
		CreateTCPTunnel(5432, "gateway-123", "*************", 5432, "PostgreSQL数据库"),
	}

	// 批量添加
	for _, config := range tunnels {
		err := GlobalTunnelManager.AddTunnel(config)
		if err != nil {
			log.Printf("添加穿透失败 [%s]: %s", config.Description, err.Error())
		} else {
			log.Printf("添加穿透成功 [%s]: %s:%d -> %s:%d",
				config.Description, config.TunnelType, config.ListenPort, config.RemoteIP, config.RemotePort)
		}
	}
}

// 示例：查询和管理穿透服务
func ExampleManageTunnels() {
	if GlobalTunnelManager == nil {
		log.Println("穿透管理器未初始化")
		return
	}

	// 获取所有穿透配置
	tunnels := GlobalTunnelManager.GetAllTunnels()
	log.Printf("当前共有 %d 个穿透服务", len(tunnels))

	// 打印所有穿透配置
	for id, config := range tunnels {
		log.Printf("穿透服务 [%s]: %s:%d -> %s:%d (%s)",
			id, config.TunnelType, config.ListenPort, config.RemoteIP, config.RemotePort, config.Description)
	}

	// 获取统计信息
	stats := GlobalTunnelManager.GetTunnelStats()
	log.Printf("穿透服务统计: %+v", stats)
}

// 示例：程序退出时的清理
func ExampleCleanup() {
	if GlobalTunnelManager != nil {
		// 保存所有配置
		err := GlobalTunnelManager.SaveAllConfigs()
		if err != nil {
			log.Printf("保存穿透配置失败: %s", err.Error())
		}

		// 停止所有服务
		GlobalTunnelManager.StopAll()
		log.Println("穿透服务清理完成")
	}
}
