// Code generated by "stringer -type=FeatureID,Vendor"; DO NOT EDIT.

package cpuid

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[ADX-1]
	_ = x[AESNI-2]
	_ = x[AMD3DNOW-3]
	_ = x[AMD3DNOWEXT-4]
	_ = x[AMXBF16-5]
	_ = x[AMXFP16-6]
	_ = x[AMXINT8-7]
	_ = x[AMXTILE-8]
	_ = x[APX_F-9]
	_ = x[AVX-10]
	_ = x[AVX10-11]
	_ = x[AVX10_128-12]
	_ = x[AVX10_256-13]
	_ = x[AVX10_512-14]
	_ = x[AVX2-15]
	_ = x[AVX512BF16-16]
	_ = x[AVX512BITALG-17]
	_ = x[AVX512BW-18]
	_ = x[AVX512CD-19]
	_ = x[AVX512DQ-20]
	_ = x[AVX512ER-21]
	_ = x[AVX512F-22]
	_ = x[AVX512FP16-23]
	_ = x[AVX512IFMA-24]
	_ = x[AVX512PF-25]
	_ = x[AVX512VBMI-26]
	_ = x[AVX512VBMI2-27]
	_ = x[AVX512VL-28]
	_ = x[AVX512VNNI-29]
	_ = x[AVX512VP2INTERSECT-30]
	_ = x[AVX512VPOPCNTDQ-31]
	_ = x[AVXIFMA-32]
	_ = x[AVXNECONVERT-33]
	_ = x[AVXSLOW-34]
	_ = x[AVXVNNI-35]
	_ = x[AVXVNNIINT8-36]
	_ = x[AVXVNNIINT16-37]
	_ = x[BHI_CTRL-38]
	_ = x[BMI1-39]
	_ = x[BMI2-40]
	_ = x[CETIBT-41]
	_ = x[CETSS-42]
	_ = x[CLDEMOTE-43]
	_ = x[CLMUL-44]
	_ = x[CLZERO-45]
	_ = x[CMOV-46]
	_ = x[CMPCCXADD-47]
	_ = x[CMPSB_SCADBS_SHORT-48]
	_ = x[CMPXCHG8-49]
	_ = x[CPBOOST-50]
	_ = x[CPPC-51]
	_ = x[CX16-52]
	_ = x[EFER_LMSLE_UNS-53]
	_ = x[ENQCMD-54]
	_ = x[ERMS-55]
	_ = x[F16C-56]
	_ = x[FLUSH_L1D-57]
	_ = x[FMA3-58]
	_ = x[FMA4-59]
	_ = x[FP128-60]
	_ = x[FP256-61]
	_ = x[FSRM-62]
	_ = x[FXSR-63]
	_ = x[FXSROPT-64]
	_ = x[GFNI-65]
	_ = x[HLE-66]
	_ = x[HRESET-67]
	_ = x[HTT-68]
	_ = x[HWA-69]
	_ = x[HYBRID_CPU-70]
	_ = x[HYPERVISOR-71]
	_ = x[IA32_ARCH_CAP-72]
	_ = x[IA32_CORE_CAP-73]
	_ = x[IBPB-74]
	_ = x[IBPB_BRTYPE-75]
	_ = x[IBRS-76]
	_ = x[IBRS_PREFERRED-77]
	_ = x[IBRS_PROVIDES_SMP-78]
	_ = x[IBS-79]
	_ = x[IBSBRNTRGT-80]
	_ = x[IBSFETCHSAM-81]
	_ = x[IBSFFV-82]
	_ = x[IBSOPCNT-83]
	_ = x[IBSOPCNTEXT-84]
	_ = x[IBSOPSAM-85]
	_ = x[IBSRDWROPCNT-86]
	_ = x[IBSRIPINVALIDCHK-87]
	_ = x[IBS_FETCH_CTLX-88]
	_ = x[IBS_OPDATA4-89]
	_ = x[IBS_OPFUSE-90]
	_ = x[IBS_PREVENTHOST-91]
	_ = x[IBS_ZEN4-92]
	_ = x[IDPRED_CTRL-93]
	_ = x[INT_WBINVD-94]
	_ = x[INVLPGB-95]
	_ = x[KEYLOCKER-96]
	_ = x[KEYLOCKERW-97]
	_ = x[LAHF-98]
	_ = x[LAM-99]
	_ = x[LBRVIRT-100]
	_ = x[LZCNT-101]
	_ = x[MCAOVERFLOW-102]
	_ = x[MCDT_NO-103]
	_ = x[MCOMMIT-104]
	_ = x[MD_CLEAR-105]
	_ = x[MMX-106]
	_ = x[MMXEXT-107]
	_ = x[MOVBE-108]
	_ = x[MOVDIR64B-109]
	_ = x[MOVDIRI-110]
	_ = x[MOVSB_ZL-111]
	_ = x[MOVU-112]
	_ = x[MPX-113]
	_ = x[MSRIRC-114]
	_ = x[MSRLIST-115]
	_ = x[MSR_PAGEFLUSH-116]
	_ = x[NRIPS-117]
	_ = x[NX-118]
	_ = x[OSXSAVE-119]
	_ = x[PCONFIG-120]
	_ = x[POPCNT-121]
	_ = x[PPIN-122]
	_ = x[PREFETCHI-123]
	_ = x[PSFD-124]
	_ = x[RDPRU-125]
	_ = x[RDRAND-126]
	_ = x[RDSEED-127]
	_ = x[RDTSCP-128]
	_ = x[RRSBA_CTRL-129]
	_ = x[RTM-130]
	_ = x[RTM_ALWAYS_ABORT-131]
	_ = x[SBPB-132]
	_ = x[SERIALIZE-133]
	_ = x[SEV-134]
	_ = x[SEV_64BIT-135]
	_ = x[SEV_ALTERNATIVE-136]
	_ = x[SEV_DEBUGSWAP-137]
	_ = x[SEV_ES-138]
	_ = x[SEV_RESTRICTED-139]
	_ = x[SEV_SNP-140]
	_ = x[SGX-141]
	_ = x[SGXLC-142]
	_ = x[SHA-143]
	_ = x[SME-144]
	_ = x[SME_COHERENT-145]
	_ = x[SPEC_CTRL_SSBD-146]
	_ = x[SRBDS_CTRL-147]
	_ = x[SRSO_MSR_FIX-148]
	_ = x[SRSO_NO-149]
	_ = x[SRSO_USER_KERNEL_NO-150]
	_ = x[SSE-151]
	_ = x[SSE2-152]
	_ = x[SSE3-153]
	_ = x[SSE4-154]
	_ = x[SSE42-155]
	_ = x[SSE4A-156]
	_ = x[SSSE3-157]
	_ = x[STIBP-158]
	_ = x[STIBP_ALWAYSON-159]
	_ = x[STOSB_SHORT-160]
	_ = x[SUCCOR-161]
	_ = x[SVM-162]
	_ = x[SVMDA-163]
	_ = x[SVMFBASID-164]
	_ = x[SVML-165]
	_ = x[SVMNP-166]
	_ = x[SVMPF-167]
	_ = x[SVMPFT-168]
	_ = x[SYSCALL-169]
	_ = x[SYSEE-170]
	_ = x[TBM-171]
	_ = x[TDX_GUEST-172]
	_ = x[TLB_FLUSH_NESTED-173]
	_ = x[TME-174]
	_ = x[TOPEXT-175]
	_ = x[TSCRATEMSR-176]
	_ = x[TSXLDTRK-177]
	_ = x[VAES-178]
	_ = x[VMCBCLEAN-179]
	_ = x[VMPL-180]
	_ = x[VMSA_REGPROT-181]
	_ = x[VMX-182]
	_ = x[VPCLMULQDQ-183]
	_ = x[VTE-184]
	_ = x[WAITPKG-185]
	_ = x[WBNOINVD-186]
	_ = x[WRMSRNS-187]
	_ = x[X87-188]
	_ = x[XGETBV1-189]
	_ = x[XOP-190]
	_ = x[XSAVE-191]
	_ = x[XSAVEC-192]
	_ = x[XSAVEOPT-193]
	_ = x[XSAVES-194]
	_ = x[AESARM-195]
	_ = x[ARMCPUID-196]
	_ = x[ASIMD-197]
	_ = x[ASIMDDP-198]
	_ = x[ASIMDHP-199]
	_ = x[ASIMDRDM-200]
	_ = x[ATOMICS-201]
	_ = x[CRC32-202]
	_ = x[DCPOP-203]
	_ = x[EVTSTRM-204]
	_ = x[FCMA-205]
	_ = x[FP-206]
	_ = x[FPHP-207]
	_ = x[GPA-208]
	_ = x[JSCVT-209]
	_ = x[LRCPC-210]
	_ = x[PMULL-211]
	_ = x[SHA1-212]
	_ = x[SHA2-213]
	_ = x[SHA3-214]
	_ = x[SHA512-215]
	_ = x[SM3-216]
	_ = x[SM4-217]
	_ = x[SVE-218]
	_ = x[lastID-219]
	_ = x[firstID-0]
}

const _FeatureID_name = "firstIDADXAESNIAMD3DNOWAMD3DNOWEXTAMXBF16AMXFP16AMXINT8AMXTILEAPX_FAVXAVX10AVX10_128AVX10_256AVX10_512AVX2AVX512BF16AVX512BITALGAVX512BWAVX512CDAVX512DQAVX512ERAVX512FAVX512FP16AVX512IFMAAVX512PFAVX512VBMIAVX512VBMI2AVX512VLAVX512VNNIAVX512VP2INTERSECTAVX512VPOPCNTDQAVXIFMAAVXNECONVERTAVXSLOWAVXVNNIAVXVNNIINT8AVXVNNIINT16BHI_CTRLBMI1BMI2CETIBTCETSSCLDEMOTECLMULCLZEROCMOVCMPCCXADDCMPSB_SCADBS_SHORTCMPXCHG8CPBOOSTCPPCCX16EFER_LMSLE_UNSENQCMDERMSF16CFLUSH_L1DFMA3FMA4FP128FP256FSRMFXSRFXSROPTGFNIHLEHRESETHTTHWAHYBRID_CPUHYPERVISORIA32_ARCH_CAPIA32_CORE_CAPIBPBIBPB_BRTYPEIBRSIBRS_PREFERREDIBRS_PROVIDES_SMPIBSIBSBRNTRGTIBSFETCHSAMIBSFFVIBSOPCNTIBSOPCNTEXTIBSOPSAMIBSRDWROPCNTIBSRIPINVALIDCHKIBS_FETCH_CTLXIBS_OPDATA4IBS_OPFUSEIBS_PREVENTHOSTIBS_ZEN4IDPRED_CTRLINT_WBINVDINVLPGBKEYLOCKERKEYLOCKERWLAHFLAMLBRVIRTLZCNTMCAOVERFLOWMCDT_NOMCOMMITMD_CLEARMMXMMXEXTMOVBEMOVDIR64BMOVDIRIMOVSB_ZLMOVUMPXMSRIRCMSRLISTMSR_PAGEFLUSHNRIPSNXOSXSAVEPCONFIGPOPCNTPPINPREFETCHIPSFDRDPRURDRANDRDSEEDRDTSCPRRSBA_CTRLRTMRTM_ALWAYS_ABORTSBPBSERIALIZESEVSEV_64BITSEV_ALTERNATIVESEV_DEBUGSWAPSEV_ESSEV_RESTRICTEDSEV_SNPSGXSGXLCSHASMESME_COHERENTSPEC_CTRL_SSBDSRBDS_CTRLSRSO_MSR_FIXSRSO_NOSRSO_USER_KERNEL_NOSSESSE2SSE3SSE4SSE42SSE4ASSSE3STIBPSTIBP_ALWAYSONSTOSB_SHORTSUCCORSVMSVMDASVMFBASIDSVMLSVMNPSVMPFSVMPFTSYSCALLSYSEETBMTDX_GUESTTLB_FLUSH_NESTEDTMETOPEXTTSCRATEMSRTSXLDTRKVAESVMCBCLEANVMPLVMSA_REGPROTVMXVPCLMULQDQVTEWAITPKGWBNOINVDWRMSRNSX87XGETBV1XOPXSAVEXSAVECXSAVEOPTXSAVESAESARMARMCPUIDASIMDASIMDDPASIMDHPASIMDRDMATOMICSCRC32DCPOPEVTSTRMFCMAFPFPHPGPAJSCVTLRCPCPMULLSHA1SHA2SHA3SHA512SM3SM4SVElastID"

var _FeatureID_index = [...]uint16{0, 7, 10, 15, 23, 34, 41, 48, 55, 62, 67, 70, 75, 84, 93, 102, 106, 116, 128, 136, 144, 152, 160, 167, 177, 187, 195, 205, 216, 224, 234, 252, 267, 274, 286, 293, 300, 311, 323, 331, 335, 339, 345, 350, 358, 363, 369, 373, 382, 400, 408, 415, 419, 423, 437, 443, 447, 451, 460, 464, 468, 473, 478, 482, 486, 493, 497, 500, 506, 509, 512, 522, 532, 545, 558, 562, 573, 577, 591, 608, 611, 621, 632, 638, 646, 657, 665, 677, 693, 707, 718, 728, 743, 751, 762, 772, 779, 788, 798, 802, 805, 812, 817, 828, 835, 842, 850, 853, 859, 864, 873, 880, 888, 892, 895, 901, 908, 921, 926, 928, 935, 942, 948, 952, 961, 965, 970, 976, 982, 988, 998, 1001, 1017, 1021, 1030, 1033, 1042, 1057, 1070, 1076, 1090, 1097, 1100, 1105, 1108, 1111, 1123, 1137, 1147, 1159, 1166, 1185, 1188, 1192, 1196, 1200, 1205, 1210, 1215, 1220, 1234, 1245, 1251, 1254, 1259, 1268, 1272, 1277, 1282, 1288, 1295, 1300, 1303, 1312, 1328, 1331, 1337, 1347, 1355, 1359, 1368, 1372, 1384, 1387, 1397, 1400, 1407, 1415, 1422, 1425, 1432, 1435, 1440, 1446, 1454, 1460, 1466, 1474, 1479, 1486, 1493, 1501, 1508, 1513, 1518, 1525, 1529, 1531, 1535, 1538, 1543, 1548, 1553, 1557, 1561, 1565, 1571, 1574, 1577, 1580, 1586}

func (i FeatureID) String() string {
	if i < 0 || i >= FeatureID(len(_FeatureID_index)-1) {
		return "FeatureID(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _FeatureID_name[_FeatureID_index[i]:_FeatureID_index[i+1]]
}
func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[VendorUnknown-0]
	_ = x[Intel-1]
	_ = x[AMD-2]
	_ = x[VIA-3]
	_ = x[Transmeta-4]
	_ = x[NSC-5]
	_ = x[KVM-6]
	_ = x[MSVM-7]
	_ = x[VMware-8]
	_ = x[XenHVM-9]
	_ = x[Bhyve-10]
	_ = x[Hygon-11]
	_ = x[SiS-12]
	_ = x[RDC-13]
	_ = x[Ampere-14]
	_ = x[ARM-15]
	_ = x[Broadcom-16]
	_ = x[Cavium-17]
	_ = x[DEC-18]
	_ = x[Fujitsu-19]
	_ = x[Infineon-20]
	_ = x[Motorola-21]
	_ = x[NVIDIA-22]
	_ = x[AMCC-23]
	_ = x[Qualcomm-24]
	_ = x[Marvell-25]
	_ = x[lastVendor-26]
}

const _Vendor_name = "VendorUnknownIntelAMDVIATransmetaNSCKVMMSVMVMwareXenHVMBhyveHygonSiSRDCAmpereARMBroadcomCaviumDECFujitsuInfineonMotorolaNVIDIAAMCCQualcommMarvelllastVendor"

var _Vendor_index = [...]uint8{0, 13, 18, 21, 24, 33, 36, 39, 43, 49, 55, 60, 65, 68, 71, 77, 80, 88, 94, 97, 104, 112, 120, 126, 130, 138, 145, 155}

func (i Vendor) String() string {
	if i < 0 || i >= Vendor(len(_Vendor_index)-1) {
		return "Vendor(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _Vendor_name[_Vendor_index[i]:_Vendor_index[i+1]]
}
