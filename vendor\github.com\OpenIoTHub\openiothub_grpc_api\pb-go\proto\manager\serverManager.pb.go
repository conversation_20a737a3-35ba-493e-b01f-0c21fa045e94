// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.26.1
// source: serverManager.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid           string `protobuf:"bytes,1,opt,name=Uuid,proto3" json:"Uuid,omitempty"`
	Name           string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`
	ServerHost     string `protobuf:"bytes,3,opt,name=ServerHost,proto3" json:"ServerHost,omitempty"`
	LoginKey       string `protobuf:"bytes,4,opt,name=LoginKey,proto3" json:"LoginKey,omitempty"`
	TcpPort        int32  `protobuf:"varint,5,opt,name=TcpPort,proto3" json:"TcpPort,omitempty"`
	KcpPort        int32  `protobuf:"varint,6,opt,name=KcpPort,proto3" json:"KcpPort,omitempty"`
	UdpApiPort     int32  `protobuf:"varint,7,opt,name=UdpApiPort,proto3" json:"UdpApiPort,omitempty"`
	KcpApiPort     int32  `protobuf:"varint,8,opt,name=KcpApiPort,proto3" json:"KcpApiPort,omitempty"`
	TlsPort        int32  `protobuf:"varint,9,opt,name=TlsPort,proto3" json:"TlsPort,omitempty"`
	GrpcPort       int32  `protobuf:"varint,10,opt,name=GrpcPort,proto3" json:"GrpcPort,omitempty"`
	Description    string `protobuf:"bytes,11,opt,name=Description,proto3" json:"Description,omitempty"`
	IsPublic       bool   `protobuf:"varint,12,opt,name=IsPublic,proto3" json:"IsPublic,omitempty"`
	IsSharedServer bool   `protobuf:"varint,13,opt,name=IsSharedServer,proto3" json:"IsSharedServer,omitempty"`
}

func (x *ServerInfo) Reset() {
	*x = ServerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_serverManager_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerInfo) ProtoMessage() {}

func (x *ServerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_serverManager_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerInfo.ProtoReflect.Descriptor instead.
func (*ServerInfo) Descriptor() ([]byte, []int) {
	return file_serverManager_proto_rawDescGZIP(), []int{0}
}

func (x *ServerInfo) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *ServerInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServerInfo) GetServerHost() string {
	if x != nil {
		return x.ServerHost
	}
	return ""
}

func (x *ServerInfo) GetLoginKey() string {
	if x != nil {
		return x.LoginKey
	}
	return ""
}

func (x *ServerInfo) GetTcpPort() int32 {
	if x != nil {
		return x.TcpPort
	}
	return 0
}

func (x *ServerInfo) GetKcpPort() int32 {
	if x != nil {
		return x.KcpPort
	}
	return 0
}

func (x *ServerInfo) GetUdpApiPort() int32 {
	if x != nil {
		return x.UdpApiPort
	}
	return 0
}

func (x *ServerInfo) GetKcpApiPort() int32 {
	if x != nil {
		return x.KcpApiPort
	}
	return 0
}

func (x *ServerInfo) GetTlsPort() int32 {
	if x != nil {
		return x.TlsPort
	}
	return 0
}

func (x *ServerInfo) GetGrpcPort() int32 {
	if x != nil {
		return x.GrpcPort
	}
	return 0
}

func (x *ServerInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ServerInfo) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

func (x *ServerInfo) GetIsSharedServer() bool {
	if x != nil {
		return x.IsSharedServer
	}
	return false
}

type ServerInfoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerInfoList []*ServerInfo `protobuf:"bytes,1,rep,name=ServerInfoList,proto3" json:"ServerInfoList,omitempty"`
}

func (x *ServerInfoList) Reset() {
	*x = ServerInfoList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_serverManager_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ServerInfoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerInfoList) ProtoMessage() {}

func (x *ServerInfoList) ProtoReflect() protoreflect.Message {
	mi := &file_serverManager_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerInfoList.ProtoReflect.Descriptor instead.
func (*ServerInfoList) Descriptor() ([]byte, []int) {
	return file_serverManager_proto_rawDescGZIP(), []int{1}
}

func (x *ServerInfoList) GetServerInfoList() []*ServerInfo {
	if x != nil {
		return x.ServerInfoList
	}
	return nil
}

var File_serverManager_proto protoreflect.FileDescriptor

var file_serverManager_proto_rawDesc = []byte{
	0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x70, 0x62, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x80, 0x03, 0x0a, 0x0a, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x55, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x55, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x48, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x4b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x4b, 0x65, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x54, 0x63, 0x70, 0x50, 0x6f,
	0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x54, 0x63, 0x70, 0x50, 0x6f, 0x72,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x4b, 0x63, 0x70, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x4b, 0x63, 0x70, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x55,
	0x64, 0x70, 0x41, 0x70, 0x69, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x55, 0x64, 0x70, 0x41, 0x70, 0x69, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x4b,
	0x63, 0x70, 0x41, 0x70, 0x69, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x4b, 0x63, 0x70, 0x41, 0x70, 0x69, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x54,
	0x6c, 0x73, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x54, 0x6c,
	0x73, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x47, 0x72, 0x70, 0x63, 0x50, 0x6f, 0x72,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x47, 0x72, 0x70, 0x63, 0x50, 0x6f, 0x72,
	0x74, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x49, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x49, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x12,
	0x26, 0x0a, 0x0e, 0x49, 0x73, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x49, 0x73, 0x53, 0x68, 0x61, 0x72, 0x65,
	0x64, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x22, 0x48, 0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x0e, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x32, 0xbd, 0x03, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x09, 0x41, 0x64, 0x64, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x12, 0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x34, 0x0a, 0x09, 0x44, 0x65, 0x6c,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x37, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12,
	0x0e, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a,
	0x15, 0x2e, 0x70, 0x62, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x41, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x00, 0x12, 0x3c, 0x0a, 0x0c, 0x47,
	0x65, 0x74, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x00, 0x12, 0x3f, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x41, 0x6c, 0x6c, 0x4d, 0x79, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73, 0x12, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x1a, 0x12, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x00, 0x12, 0x45, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x6c, 0x4d, 0x79, 0x53, 0x68, 0x61, 0x72, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x12, 0x2e, 0x70, 0x62,
	0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x00, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_serverManager_proto_rawDescOnce sync.Once
	file_serverManager_proto_rawDescData = file_serverManager_proto_rawDesc
)

func file_serverManager_proto_rawDescGZIP() []byte {
	file_serverManager_proto_rawDescOnce.Do(func() {
		file_serverManager_proto_rawDescData = protoimpl.X.CompressGZIP(file_serverManager_proto_rawDescData)
	})
	return file_serverManager_proto_rawDescData
}

var file_serverManager_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_serverManager_proto_goTypes = []interface{}{
	(*ServerInfo)(nil),             // 0: pb.ServerInfo
	(*ServerInfoList)(nil),         // 1: pb.ServerInfoList
	(*wrapperspb.StringValue)(nil), // 2: google.protobuf.StringValue
	(*emptypb.Empty)(nil),          // 3: google.protobuf.Empty
	(*OperationResponse)(nil),      // 4: pb.OperationResponse
}
var file_serverManager_proto_depIdxs = []int32{
	0, // 0: pb.ServerInfoList.ServerInfoList:type_name -> pb.ServerInfo
	0, // 1: pb.ServerManager.AddServer:input_type -> pb.ServerInfo
	0, // 2: pb.ServerManager.DelServer:input_type -> pb.ServerInfo
	0, // 3: pb.ServerManager.UpdateServer:input_type -> pb.ServerInfo
	2, // 4: pb.ServerManager.QueryServer:input_type -> google.protobuf.StringValue
	3, // 5: pb.ServerManager.GetAllServer:input_type -> google.protobuf.Empty
	3, // 6: pb.ServerManager.GetAllMyServers:input_type -> google.protobuf.Empty
	3, // 7: pb.ServerManager.GetAllMySharedServers:input_type -> google.protobuf.Empty
	4, // 8: pb.ServerManager.AddServer:output_type -> pb.OperationResponse
	4, // 9: pb.ServerManager.DelServer:output_type -> pb.OperationResponse
	4, // 10: pb.ServerManager.UpdateServer:output_type -> pb.OperationResponse
	1, // 11: pb.ServerManager.QueryServer:output_type -> pb.ServerInfoList
	1, // 12: pb.ServerManager.GetAllServer:output_type -> pb.ServerInfoList
	1, // 13: pb.ServerManager.GetAllMyServers:output_type -> pb.ServerInfoList
	1, // 14: pb.ServerManager.GetAllMySharedServers:output_type -> pb.ServerInfoList
	8, // [8:15] is the sub-list for method output_type
	1, // [1:8] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_serverManager_proto_init() }
func file_serverManager_proto_init() {
	if File_serverManager_proto != nil {
		return
	}
	file_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_serverManager_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_serverManager_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ServerInfoList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_serverManager_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_serverManager_proto_goTypes,
		DependencyIndexes: file_serverManager_proto_depIdxs,
		MessageInfos:      file_serverManager_proto_msgTypes,
	}.Build()
	File_serverManager_proto = out.File
	file_serverManager_proto_rawDesc = nil
	file_serverManager_proto_goTypes = nil
	file_serverManager_proto_depIdxs = nil
}
