package manager

import (
	"fmt"
	"strconv"
	"strings"
)

// 命令处理函数类型
type CmdHandler func(args []string) error

// 命令帮助信息
var cmdHelp = map[string]string{
	"tunnel list":    "列出所有穿透配置",
	"tunnel add":     "添加穿透配置: tunnel add <类型> [本地端口] <远程IP> <远程端口> <网关ID> [描述]",
	"tunnel remove":  "删除穿透配置: tunnel remove <穿透ID>",
	"tunnel stats":   "查看穿透统计: tunnel stats [穿透ID]",
	"tunnel restart": "重启穿透服务: tunnel restart <穿透ID>",
	"tunnel help":    "显示帮助信息",
}

// 处理穿透相关命令
func (tm *TunnelManager) HandleCommand(cmd string, args []string) error {
	handlers := map[string]CmdHandler{
		"list":    tm.handleList,
		"add":     tm.handleAdd,
		"remove":  tm.handleRemove,
		"stats":   tm.handleStats,
		"restart": tm.handleRestart,
		"help":    tm.handleHelp,
	}

	if handler, exists := handlers[cmd]; exists {
		return handler(args)
	}

	return fmt.Errorf("未知命令: %s\n可用命令:\n%s", cmd, tm.getHelpText())
}

// 列出所有穿透配置
func (tm *TunnelManager) handleList(args []string) error {
	tunnels := tm.GetAllTunnels()
	if len(tunnels) == 0 {
		fmt.Println("当前没有穿透配置")
		return nil
	}

	fmt.Println("\n当前穿透配置列表:")
	fmt.Println("----------------------------------------")
	for id, config := range tunnels {
		status := "已停止"
		if config.Status {
			status = "运行中"
		}
		fmt.Printf("ID: %s\n", id)
		fmt.Printf("类型: %s\n", config.TunnelType)
		fmt.Printf("本地端口: %d\n", config.ListenPort)
		fmt.Printf("远程地址: %s:%d\n", config.RemoteIP, config.RemotePort)
		fmt.Printf("网关ID: %s\n", config.RunId)
		fmt.Printf("状态: %s\n", status)
		if config.Description != "" {
			fmt.Printf("描述: %s\n", config.Description)
		}
		if config.Metrics != nil {
			fmt.Printf("活跃连接: %d\n", config.Metrics.ActiveConnections)
			fmt.Printf("总连接数: %d\n", config.Metrics.TotalConnections)
			fmt.Printf("总传输量: %d 字节\n", config.Metrics.TotalBytes)
		}
		fmt.Printf("最后活跃: %v\n", config.LastActive.Format("2006-01-02 15:04:05"))
		fmt.Println("----------------------------------------")
	}
	return nil
}

// 添加穿透配置
func (tm *TunnelManager) handleAdd(args []string) error {
	if len(args) < 4 {
		return fmt.Errorf("参数不足\n用法: tunnel add <类型> [本地端口] <远程IP> <远程端口> <网关ID> [描述]")
	}

	tunnelType := strings.ToLower(args[0])

	var listenPort int = 0 // 默认为0，表示自动分配端口
	var err error
	var argIndex int = 1

	// 检查第二个参数是否是端口号
	if len(args) > 1 {
		if port, err := strconv.Atoi(args[1]); err == nil {
			// 如果第二个参数是数字，则认为是指定的端口
			listenPort = port
			argIndex = 2 // 后续参数往后移一位
		}
	}

	// 检查剩余参数是否足够
	if len(args) < argIndex+3 {
		return fmt.Errorf("参数不足\n用法: tunnel add <类型> [本地端口] <远程IP> <远程端口> <网关ID> [描述]")
	}

	remoteIP := args[argIndex]
	remotePort, err := strconv.Atoi(args[argIndex+1])
	if err != nil {
		return fmt.Errorf("无效的远程端口: %s", args[argIndex+1])
	}

	runId := args[argIndex+2]
	description := ""
	if len(args) > argIndex+3 {
		description = args[argIndex+3]
	}

	var config *TunnelConfig
	switch tunnelType {
	case "tcp":
		config = CreateTCPTunnel(listenPort, runId, remoteIP, remotePort, description)
	case "ssh":
		// SSH穿透需要用户名和密码
		if len(args) < argIndex+5 {
			if listenPort > 0 {
				return fmt.Errorf("SSH穿透需要用户名和密码\n用法: tunnel add ssh <本地端口> <远程IP> <远程端口> <网关ID> <用户名> <密码> [描述]")
			} else {
				return fmt.Errorf("SSH穿透需要用户名和密码\n用法: tunnel add ssh [本地端口] <远程IP> <远程端口> <网关ID> <用户名> <密码> [描述]")
			}
		}

		userName := args[argIndex+3]
		password := args[argIndex+4]
		description = ""
		if len(args) > argIndex+5 {
			description = args[argIndex+5]
		}
		config = CreateSSHTunnel(listenPort, runId, remoteIP, remotePort, userName, password, description)
	case "ws", "wss":
		// WebSocket穿透需要额外参数
		if len(args) < argIndex+6 {
			if listenPort > 0 {
				return fmt.Errorf("WebSocket穿透需要额外参数\n用法: tunnel add ws <本地端口> <远程IP> <远程端口> <网关ID> <协议> <origin> <targetUrl> [描述]")
			} else {
				return fmt.Errorf("WebSocket穿透需要额外参数\n用法: tunnel add ws [本地端口] <远程IP> <远程端口> <网关ID> <协议> <origin> <targetUrl> [描述]")
			}
		}

		protocol := args[argIndex+3]
		origin := args[argIndex+4]
		targetUrl := args[argIndex+5]
		description = ""
		if len(args) > argIndex+6 {
			description = args[argIndex+6]
		}
		config = CreateWebSocketTunnel(listenPort, runId, remoteIP, remotePort, protocol, origin, targetUrl, description)
	default:
		return fmt.Errorf("不支持的穿透类型: %s\n支持的类型: tcp, ssh, ws, wss", tunnelType)
	}

	err = tm.AddTunnel(config)
	if err != nil {
		return fmt.Errorf("添加穿透失败: %v", err)
	}

	fmt.Printf("穿透服务添加成功: %s [%s:%d -> %s:%d]\n",
		config.ID, config.TunnelType, config.ListenPort, config.RemoteIP, config.RemotePort)
	return nil
}

// 删除穿透配置
func (tm *TunnelManager) handleRemove(args []string) error {
	if len(args) < 1 {
		return fmt.Errorf("请指定要删除的穿透ID\n用法: tunnel remove <穿透ID>")
	}

	tunnelID := args[0]
	err := tm.RemoveTunnel(tunnelID)
	if err != nil {
		return fmt.Errorf("删除穿透失败: %v", err)
	}

	fmt.Printf("穿透服务删除成功: %s\n", tunnelID)
	return nil
}

// 查看穿透统计
func (tm *TunnelManager) handleStats(args []string) error {
	if len(args) > 0 {
		// 查看指定穿透的统计
		tunnelID := args[0]
		config, err := tm.GetTunnel(tunnelID)
		if err != nil {
			return fmt.Errorf("获取穿透配置失败: %v", err)
		}

		fmt.Printf("\n穿透服务统计 [%s]:\n", tunnelID)
		fmt.Println("----------------------------------------")
		fmt.Printf("类型: %s\n", config.TunnelType)
		fmt.Printf("本地端口: %d\n", config.ListenPort)
		fmt.Printf("远程地址: %s:%d\n", config.RemoteIP, config.RemotePort)
		if config.Metrics != nil {
			fmt.Printf("当前连接数: %d\n", config.Metrics.ActiveConnections)
			fmt.Printf("总连接数: %d\n", config.Metrics.TotalConnections)
			fmt.Printf("总传输量: %d 字节\n", config.Metrics.TotalBytes)
			fmt.Printf("错误次数: %d\n", config.Metrics.ErrorCount)
			fmt.Printf("最后错误: %v\n", config.Metrics.LastErrorTime.Format("2006-01-02 15:04:05"))
		}
		fmt.Printf("最后活跃: %v\n", config.LastActive.Format("2006-01-02 15:04:05"))
		return nil
	}

	// 查看总体统计
	stats := tm.GetTunnelStats()
	fmt.Println("\n穿透服务总体统计:")
	fmt.Println("----------------------------------------")
	fmt.Printf("总穿透数: %d\n", stats["totalTunnels"])
	fmt.Printf("活跃穿透: %d\n", stats["activeTunnels"])
	fmt.Printf("总连接数: %d\n", stats["totalConnections"])
	fmt.Printf("总传输量: %d 字节\n", stats["totalBytes"])
	fmt.Printf("总错误数: %d\n", stats["totalErrors"])

	fmt.Println("\n按类型统计:")
	typeCount := stats["tunnelsByType"].(map[string]int)
	for t, count := range typeCount {
		fmt.Printf("%s: %d\n", t, count)
	}

	return nil
}

// 重启穿透服务
func (tm *TunnelManager) handleRestart(args []string) error {
	if len(args) < 1 {
		return fmt.Errorf("请指定要重启的穿透ID\n用法: tunnel restart <穿透ID>")
	}

	tunnelID := args[0]
	err := tm.RestartTunnel(tunnelID)
	if err != nil {
		return fmt.Errorf("重启穿透失败: %v", err)
	}

	fmt.Printf("穿透服务重启成功: %s\n", tunnelID)
	return nil
}

// 显示帮助信息
func (tm *TunnelManager) handleHelp(args []string) error {
	fmt.Print(tm.getHelpText())
	return nil
}

// 获取帮助文本
func (tm *TunnelManager) getHelpText() string {
	var help strings.Builder
	help.WriteString("\n可用命令:\n")
	help.WriteString("----------------------------------------\n")
	for cmd, desc := range cmdHelp {
		help.WriteString(fmt.Sprintf("%-20s %s\n", cmd, desc))
	}
	return help.String()
}
