package manager

import (
	"encoding/json"
	"net/http"
	"strings"
)

// AuthMiddleware 认证中间件
type AuthMiddleware struct {
	APIKey  string
	Enabled bool
}

// NewAuthMiddleware 创建新的认证中间件
func NewAuthMiddleware(apiKey string, enabled bool) *AuthMiddleware {
	return &AuthMiddleware{
		APIKey:  apiKey,
		Enabled: enabled,
	}
}

// Middleware 中间件处理函数
func (am *AuthMiddleware) Middleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 如果认证被禁用，直接放行
		if !am.Enabled {
			next(w, r)
			return
		}

		// 从请求头获取Authorization
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			sendUnauthorizedResponse(w)
			return
		}

		// 验证Bearer格式
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			sendUnauthorizedResponse(w)
			return
		}

		// 验证API密钥
		if parts[1] != am.APIKey {
			sendUnauthorizedResponse(w)
			return
		}

		// 认证通过，继续处理请求
		next(w, r)
	}
}

// 发送未授权响应
func sendUnauthorizedResponse(w http.ResponseWriter) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusUnauthorized)
	response := map[string]interface{}{
		"code":    401,
		"message": "未授权访问，请提供有效的API密钥",
	}
	json.NewEncoder(w).Encode(response)
}
