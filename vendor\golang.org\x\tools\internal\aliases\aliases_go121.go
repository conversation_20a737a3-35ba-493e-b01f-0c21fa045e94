// Copyright 2024 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

//go:build !go1.22
// +build !go1.22

package aliases

import (
	"go/types"
)

// <PERSON><PERSON> is a placeholder for a go/types.Alias for <=1.21.
// It will never be created by go/types.
type Alias struct{}

func (*Alias) String() string                                { panic("unreachable") }
func (*<PERSON>as) Underlying() types.Type                        { panic("unreachable") }
func (*<PERSON>as) Obj() *types.TypeName                          { panic("unreachable") }
func Rhs(alias *<PERSON><PERSON>) types.Type                            { panic("unreachable") }
func TypeParams(alias *<PERSON>as) *types.TypeParamList           { panic("unreachable") }
func SetTypeParams(alias *<PERSON>as, tparams []*types.TypeParam) { panic("unreachable") }
func TypeArgs(alias *<PERSON>as) *types.TypeList                  { panic("unreachable") }
func Origin(alias *<PERSON><PERSON>) *<PERSON><PERSON>                             { panic("unreachable") }

// <PERSON><PERSON><PERSON> returns the type t for go <=1.21.
func Unalias(t types.Type) types.Type { return t }

func newAlias(name *types.TypeName, rhs types.Type) *Alias { panic("unreachable") }

// Enabled reports whether [NewAlias] should create [types.Alias] types.
//
// Before go1.22, this function always returns false.
func Enabled() bool { return false }
