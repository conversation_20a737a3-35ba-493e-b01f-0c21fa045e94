// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.26.1
// source: hostManager.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 手动添加的局域网主机
type HostInfoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostInfoList []*HostInfo `protobuf:"bytes,1,rep,name=HostInfoList,proto3" json:"HostInfoList,omitempty"`
}

func (x *HostInfoList) Reset() {
	*x = HostInfoList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hostManager_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HostInfoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostInfoList) ProtoMessage() {}

func (x *HostInfoList) ProtoReflect() protoreflect.Message {
	mi := &file_hostManager_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostInfoList.ProtoReflect.Descriptor instead.
func (*HostInfoList) Descriptor() ([]byte, []int) {
	return file_hostManager_proto_rawDescGZIP(), []int{0}
}

func (x *HostInfoList) GetHostInfoList() []*HostInfo {
	if x != nil {
		return x.HostInfoList
	}
	return nil
}

type HostInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UUID        string `protobuf:"bytes,1,opt,name=UUID,proto3" json:"UUID,omitempty"`
	Name        string `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`
	Description string `protobuf:"bytes,3,opt,name=Description,proto3" json:"Description,omitempty"`
	HostAddr    string `protobuf:"bytes,4,opt,name=HostAddr,proto3" json:"HostAddr,omitempty"`
	GatewayUUID string `protobuf:"bytes,5,opt,name=GatewayUUID,proto3" json:"GatewayUUID,omitempty"`
	Mac         string `protobuf:"bytes,6,opt,name=Mac,proto3" json:"Mac,omitempty"`
}

func (x *HostInfo) Reset() {
	*x = HostInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hostManager_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HostInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostInfo) ProtoMessage() {}

func (x *HostInfo) ProtoReflect() protoreflect.Message {
	mi := &file_hostManager_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostInfo.ProtoReflect.Descriptor instead.
func (*HostInfo) Descriptor() ([]byte, []int) {
	return file_hostManager_proto_rawDescGZIP(), []int{1}
}

func (x *HostInfo) GetUUID() string {
	if x != nil {
		return x.UUID
	}
	return ""
}

func (x *HostInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *HostInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *HostInfo) GetHostAddr() string {
	if x != nil {
		return x.HostAddr
	}
	return ""
}

func (x *HostInfo) GetGatewayUUID() string {
	if x != nil {
		return x.GatewayUUID
	}
	return ""
}

func (x *HostInfo) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

var File_hostManager_proto protoreflect.FileDescriptor

var file_hostManager_proto_rawDesc = []byte{
	0x0a, 0x11, 0x68, 0x6f, 0x73, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x02, 0x70, 0x62, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x40, 0x0a, 0x0c, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x30, 0x0a, 0x0c, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x48, 0x6f,
	0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0xa4, 0x01, 0x0a, 0x08, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x12, 0x0a, 0x04, 0x55, 0x55, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x55, 0x55, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x48,
	0x6f, 0x73, 0x74, 0x41, 0x64, 0x64, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x48,
	0x6f, 0x73, 0x74, 0x41, 0x64, 0x64, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x47, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x55, 0x55, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x47, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x55, 0x55, 0x49, 0x44, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x61, 0x63,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x4d, 0x61, 0x63, 0x32, 0x98, 0x02, 0x0a, 0x0b,
	0x48, 0x6f, 0x73, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0b, 0x47,
	0x65, 0x74, 0x41, 0x6c, 0x6c, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x10, 0x2e, 0x70, 0x62, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0x00, 0x12, 0x30, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x48, 0x6f, 0x73,
	0x74, 0x12, 0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x1a,
	0x15, 0x2e, 0x70, 0x62, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x33, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x48, 0x6f, 0x73, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x30, 0x0a,
	0x07, 0x44, 0x65, 0x6c, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x48, 0x6f,
	0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x35, 0x0a, 0x0c, 0x53, 0x65, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x61, 0x63, 0x12,
	0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x15, 0x2e,
	0x70, 0x62, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x3b, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_hostManager_proto_rawDescOnce sync.Once
	file_hostManager_proto_rawDescData = file_hostManager_proto_rawDesc
)

func file_hostManager_proto_rawDescGZIP() []byte {
	file_hostManager_proto_rawDescOnce.Do(func() {
		file_hostManager_proto_rawDescData = protoimpl.X.CompressGZIP(file_hostManager_proto_rawDescData)
	})
	return file_hostManager_proto_rawDescData
}

var file_hostManager_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_hostManager_proto_goTypes = []interface{}{
	(*HostInfoList)(nil),      // 0: pb.HostInfoList
	(*HostInfo)(nil),          // 1: pb.HostInfo
	(*emptypb.Empty)(nil),     // 2: google.protobuf.Empty
	(*OperationResponse)(nil), // 3: pb.OperationResponse
}
var file_hostManager_proto_depIdxs = []int32{
	1, // 0: pb.HostInfoList.HostInfoList:type_name -> pb.HostInfo
	2, // 1: pb.HostManager.GetAllHosts:input_type -> google.protobuf.Empty
	1, // 2: pb.HostManager.AddHost:input_type -> pb.HostInfo
	1, // 3: pb.HostManager.UpdateHost:input_type -> pb.HostInfo
	1, // 4: pb.HostManager.DelHost:input_type -> pb.HostInfo
	1, // 5: pb.HostManager.SetDeviceMac:input_type -> pb.HostInfo
	0, // 6: pb.HostManager.GetAllHosts:output_type -> pb.HostInfoList
	3, // 7: pb.HostManager.AddHost:output_type -> pb.OperationResponse
	3, // 8: pb.HostManager.UpdateHost:output_type -> pb.OperationResponse
	3, // 9: pb.HostManager.DelHost:output_type -> pb.OperationResponse
	3, // 10: pb.HostManager.SetDeviceMac:output_type -> pb.OperationResponse
	6, // [6:11] is the sub-list for method output_type
	1, // [1:6] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_hostManager_proto_init() }
func file_hostManager_proto_init() {
	if File_hostManager_proto != nil {
		return
	}
	file_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_hostManager_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HostInfoList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hostManager_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HostInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hostManager_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hostManager_proto_goTypes,
		DependencyIndexes: file_hostManager_proto_depIdxs,
		MessageInfos:      file_hostManager_proto_msgTypes,
	}.Build()
	File_hostManager_proto = out.File
	file_hostManager_proto_rawDesc = nil
	file_hostManager_proto_goTypes = nil
	file_hostManager_proto_depIdxs = nil
}
