package manager

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math/rand"
	"net"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/OpenIoTHub/server-go/config"
	"github.com/OpenIoTHub/utils/models"
)

// 错误定义
var (
	ErrTunnelNotFound     = errors.New("穿透配置不存在")
	ErrTunnelExists       = errors.New("穿透配置已存在")
	ErrInvalidConfig      = errors.New("无效的穿透配置")
	ErrTooManyConnections = errors.New("连接数超过限制")
)

// 穿透类型常量
const (
	TunnelTypeTCP        = "tcp"
	TunnelTypeTLS        = "tls"
	TunnelTypeUDP        = "udp"
	TunnelTypeWebSocket  = "ws"
	TunnelTypeWebSocketS = "wss"
	TunnelTypeSSH        = "ssh"
	TunnelTypeSerialPort = "serial"
)

// 默认配置
const (
	DefaultInactiveTimeout  = 24 * time.Hour // 默认24小时无活动自动清理
	CleanupInterval         = 1 * time.Hour  // 清理检查间隔
	MaxConnectionsPerTunnel = 100            // 每个穿透的最大连接数
	MaxRetries              = 3              // 最大重试次数
	RetryInterval           = time.Second    // 重试间隔
	BufferPoolSize          = 1024           // 缓冲区池大小
)

// 监控指标结构
type TunnelMetrics struct {
	ActiveConnections int64     // 当前活跃连接数
	TotalConnections  int64     // 总连接数
	TotalBytes        int64     // 总传输字节数
	LastErrorTime     time.Time // 最后一次错误时间
	ErrorCount        int64     // 错误计数
}

// 通用穿透配置结构
type TunnelConfig struct {
	ID          string            `json:"id"`          // 唯一标识符
	TunnelType  string            `json:"tunnelType"`  // 穿透类型
	ListenPort  int               `json:"listenPort"`  // 服务器监听端口
	RunId       string            `json:"runId"`       // 网关ID
	RemoteIP    string            `json:"remoteIP"`    // 远程服务IP
	RemotePort  int               `json:"remotePort"`  // 远程服务端口
	ExtraParams map[string]string `json:"extraParams"` // 额外参数
	Description string            `json:"description"` // 描述
	Status      bool              `json:"status"`      // 运行状态
	LastActive  time.Time         `json:"lastActive"`  // 最后活跃时间
	Metrics     *TunnelMetrics    `json:"metrics"`     // 监控指标
	listener    net.Listener      // 监听器，用于停止服务
}

// 穿透管理器
type TunnelManager struct {
	sessionManager  *SessionsManager
	tunnels         map[string]*TunnelConfig // ID -> 配置
	listeners       map[string]net.Listener  // ID -> 监听器
	mutex           sync.RWMutex
	inactiveTimeout time.Duration  // 不活跃超时时间
	cleanupTimer    *time.Timer    // 清理定时器
	stopChan        chan struct{}  // 停止信号通道
	wg              sync.WaitGroup // 等待组，用于优雅退出
	bufferPool      sync.Pool      // 缓冲区对象池
	portRangeStart  int            // 端口区间起始
	portRangeEnd    int            // 端口区间结束
	usedPorts       map[int]bool   // 已使用的端口
	portRangeMutex  sync.Mutex     // 端口区间互斥锁
}

// 创建穿透管理器
func NewTunnelManager(sm *SessionsManager) *TunnelManager {
	tm := &TunnelManager{
		sessionManager:  sm,
		tunnels:         make(map[string]*TunnelConfig),
		listeners:       make(map[string]net.Listener),
		inactiveTimeout: DefaultInactiveTimeout,
		stopChan:        make(chan struct{}),
		bufferPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 32*1024)
			},
		},
		portRangeStart: config.DefaultTunnelPortRangeStart,
		portRangeEnd:   config.DefaultTunnelPortRangeEnd,
		usedPorts:      make(map[int]bool),
	}

	// 启动自动清理
	tm.startCleanupTimer()

	return tm
}

// 生成唯一ID
func (tm *TunnelManager) generateTunnelID(tunnelType string, port int) string {
	return fmt.Sprintf("%s_%d", tunnelType, port)
}

// 添加或更新穿透配置
func (tm *TunnelManager) AddTunnel(config *TunnelConfig) error {
	if config == nil {
		return ErrInvalidConfig
	}

	// 检查是否需要自动分配端口
	needPort := config.ListenPort == 0

	// 如果需要自动分配端口，先分配
	if needPort {
		port, err := tm.allocatePort()
		if err != nil {
			return err
		}
		config.ListenPort = port
	}

	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	// 生成ID
	if config.ID == "" {
		config.ID = tm.generateTunnelID(config.TunnelType, config.ListenPort)
	}

	// 检查是否已存在相同ID的配置
	if existingConfig, exists := tm.tunnels[config.ID]; exists {
		// 停止现有服务
		tm.stopTunnelUnsafe(existingConfig.ID)
	}

	// 初始化监控指标
	config.Metrics = &TunnelMetrics{
		LastErrorTime: time.Now(),
	}

	// 初始化最后活跃时间
	config.LastActive = time.Now()

	// 启动穿透服务
	for i := 0; i < MaxRetries; i++ {
		err := tm.startTunnelUnsafe(config)
		if err == nil {
			break
		}
		if i == MaxRetries-1 {
			// 如果是自动分配的端口且启动失败，释放端口
			if needPort {
				tm.releasePort(config.ListenPort)
			}
			return fmt.Errorf("启动穿透服务失败: %w", err)
		}
		time.Sleep(RetryInterval)
	}

	// 保存配置
	tm.tunnels[config.ID] = config
	config.Status = true

	log.Printf("穿透服务添加成功: %s [%s:%d -> %s:%d]",
		config.ID, config.TunnelType, config.ListenPort, config.RemoteIP, config.RemotePort)

	return nil
}

// 删除穿透配置
func (tm *TunnelManager) RemoveTunnel(tunnelID string) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	config, exists := tm.tunnels[tunnelID]
	if !exists {
		return errors.New(fmt.Sprintf("穿透配置不存在: %s", tunnelID))
	}

	// 停止服务
	tm.stopTunnelUnsafe(tunnelID)

	// 释放端口
	if config.ListenPort >= tm.portRangeStart && config.ListenPort <= tm.portRangeEnd {
		tm.releasePort(config.ListenPort)
	}

	// 删除配置
	delete(tm.tunnels, tunnelID)

	log.Printf("穿透服务删除成功: %s", tunnelID)
	return nil
}

// 获取所有穿透配置
func (tm *TunnelManager) GetAllTunnels() map[string]*TunnelConfig {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	result := make(map[string]*TunnelConfig)
	for id, config := range tm.tunnels {
		// 创建副本，避免并发问题
		configCopy := *config
		result[id] = &configCopy
	}
	return result
}

// 获取指定穿透配置
func (tm *TunnelManager) GetTunnel(tunnelID string) (*TunnelConfig, error) {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	config, exists := tm.tunnels[tunnelID]
	if !exists {
		return nil, errors.New(fmt.Sprintf("穿透配置不存在: %s", tunnelID))
	}

	// 返回副本
	configCopy := *config
	return &configCopy, nil
}

// 启动穿透服务（内部方法，需要加锁）
func (tm *TunnelManager) startTunnelUnsafe(config *TunnelConfig) error {
	// 检查端口是否被占用
	testListener, err := net.Listen("tcp", fmt.Sprintf(":%d", config.ListenPort))
	if err != nil {
		return fmt.Errorf("端口 %d 已被占用: %w", config.ListenPort, err)
	}
	testListener.Close()

	// 创建正式监听器
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", config.ListenPort))
	if err != nil {
		return fmt.Errorf("无法监听端口 %d: %w", config.ListenPort, err)
	}

	// 保存监听器
	tm.listeners[config.ID] = listener
	config.listener = listener

	// 启动连接处理协程
	go tm.handleConnections(config, listener)

	log.Printf("%s穿透服务启动，监听端口: %d -> %s:%d",
		config.TunnelType, config.ListenPort, config.RemoteIP, config.RemotePort)

	return nil
}

// 停止穿透服务（内部方法，需要加锁）
func (tm *TunnelManager) stopTunnelUnsafe(tunnelID string) {
	if listener, exists := tm.listeners[tunnelID]; exists {
		listener.Close()
		delete(tm.listeners, tunnelID)
	}

	if config, exists := tm.tunnels[tunnelID]; exists {
		config.Status = false
		config.listener = nil
	}
}

// 处理连接
func (tm *TunnelManager) handleConnections(config *TunnelConfig, listener net.Listener) {
	for {
		// 接受新连接
		clientConn, err := listener.Accept()
		if err != nil {
			// 监听器可能已关闭
			log.Printf("接受连接失败 [%s]: %s", config.ID, err.Error())
			return
		}

		// 处理每个连接
		go tm.handleSingleConnection(clientConn, config)
	}
}

// 处理单个连接
func (tm *TunnelManager) handleSingleConnection(clientConn net.Conn, config *TunnelConfig) {
	defer clientConn.Close()

	// 检查连接数限制
	currentConns := atomic.LoadInt64(&config.Metrics.ActiveConnections)
	if currentConns >= MaxConnectionsPerTunnel {
		log.Printf("连接数超过限制 [%s]: %d", config.ID, currentConns)
		return
	}

	// 更新连接计数
	atomic.AddInt64(&config.Metrics.ActiveConnections, 1)
	atomic.AddInt64(&config.Metrics.TotalConnections, 1)
	defer atomic.AddInt64(&config.Metrics.ActiveConnections, -1)

	// 更新活跃时间
	tm.updateTunnelActivity(config.ID)

	// 创建远程连接（带重试）
	var remoteConn net.Conn
	var err error
	for i := 0; i < MaxRetries; i++ {
		remoteConn, err = tm.createRemoteConnection(config)
		if err == nil {
			break
		}
		if i == MaxRetries-1 {
			tm.recordError(config, fmt.Errorf("创建远程连接失败: %w", err))
			return
		}
		time.Sleep(RetryInterval)
	}
	defer remoteConn.Close()

	// 创建用于监控数据传输的通道
	activityChan := make(chan struct{}, 1)

	// 启动数据传输和活跃度监控
	tm.wg.Add(2)
	go func() {
		defer tm.wg.Done()
		buf := tm.bufferPool.Get().([]byte)
		defer tm.bufferPool.Put(buf)

		for {
			n, err := clientConn.Read(buf)
			if err != nil {
				return
			}
			if n > 0 {
				atomic.AddInt64(&config.Metrics.TotalBytes, int64(n))
				select {
				case activityChan <- struct{}{}:
				default:
				}
				if _, err := remoteConn.Write(buf[:n]); err != nil {
					tm.recordError(config, err)
					return
				}
			}
		}
	}()

	go func() {
		defer tm.wg.Done()
		buf := tm.bufferPool.Get().([]byte)
		defer tm.bufferPool.Put(buf)

		for {
			n, err := remoteConn.Read(buf)
			if err != nil {
				return
			}
			if n > 0 {
				atomic.AddInt64(&config.Metrics.TotalBytes, int64(n))
				select {
				case activityChan <- struct{}{}:
				default:
				}
				if _, err := clientConn.Write(buf[:n]); err != nil {
					tm.recordError(config, err)
					return
				}
			}
		}
	}()

	// 监控数据传输活动
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-activityChan:
			tm.updateTunnelActivity(config.ID)
		case <-ticker.C:
			if !tm.isTunnelActive(config.ID) {
				return
			}
		case <-tm.stopChan:
			return
		}
	}
}

// 记录错误
func (tm *TunnelManager) recordError(config *TunnelConfig, err error) {
	atomic.AddInt64(&config.Metrics.ErrorCount, 1)
	config.Metrics.LastErrorTime = time.Now()
	log.Printf("穿透错误 [%s]: %s", config.ID, err.Error())
}

// 检查穿透是否还活跃
func (tm *TunnelManager) isTunnelActive(tunnelID string) bool {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	config, exists := tm.tunnels[tunnelID]
	if !exists || !config.Status {
		return false
	}

	if time.Since(config.LastActive) > tm.inactiveTimeout {
		return false
	}

	return true
}

// 创建远程连接
func (tm *TunnelManager) createRemoteConnection(config *TunnelConfig) (net.Conn, error) {
	switch config.TunnelType {
	case TunnelTypeTCP, TunnelTypeSSH:
		// SSH和TCP穿透都使用TCP连接
		// 这种方式可以避免SSH协议握手问题，同时保持类型区分便于管理
		if config.TunnelType == TunnelTypeSSH {
			log.Printf("使用TCP模式连接SSH服务: %s:%d", config.RemoteIP, config.RemotePort)
		}
		return tm.sessionManager.ConnectToTcp(config.RunId, config.RemoteIP, config.RemotePort)

	case TunnelTypeTLS:
		return tm.sessionManager.ConnectToTls(config.RunId, config.RemoteIP, config.RemotePort)

	case TunnelTypeUDP:
		return tm.sessionManager.ConnectToUdp(config.RunId, config.RemoteIP, config.RemotePort)

	case TunnelTypeWebSocket:
		protocol := config.ExtraParams["protocol"]
		origin := config.ExtraParams["origin"]
		targetUrl := config.ExtraParams["targetUrl"]
		if targetUrl == "" {
			targetUrl = fmt.Sprintf("ws://%s:%d", config.RemoteIP, config.RemotePort)
		}
		return tm.sessionManager.ConnectToWs(config.RunId, targetUrl, protocol, origin)

	case TunnelTypeWebSocketS:
		protocol := config.ExtraParams["protocol"]
		origin := config.ExtraParams["origin"]
		targetUrl := config.ExtraParams["targetUrl"]
		if targetUrl == "" {
			targetUrl = fmt.Sprintf("wss://%s:%d", config.RemoteIP, config.RemotePort)
		}
		return tm.sessionManager.ConnectToWs(config.RunId, targetUrl, protocol, origin)

	case TunnelTypeSerialPort:
		return tm.createSerialPortConnection(config)

	default:
		return nil, fmt.Errorf("不支持的穿透类型: %s", config.TunnelType)
	}
}

// 创建串口连接
func (tm *TunnelManager) createSerialPortConnection(config *TunnelConfig) (net.Conn, error) {
	portName := config.ExtraParams["portName"]
	baudRateStr := config.ExtraParams["baudRate"]
	dataBitsStr := config.ExtraParams["dataBits"]
	stopBitsStr := config.ExtraParams["stopBits"]
	minimumReadSizeStr := config.ExtraParams["minimumReadSize"]

	// 设置默认值
	baudRate := uint(9600)
	dataBits := uint(8)
	stopBits := uint(1)
	minimumReadSize := uint(4)

	// 解析参数
	if baudRateStr != "" {
		if rate, err := strconv.ParseUint(baudRateStr, 10, 32); err == nil {
			baudRate = uint(rate)
		}
	}
	if dataBitsStr != "" {
		if bits, err := strconv.ParseUint(dataBitsStr, 10, 32); err == nil {
			dataBits = uint(bits)
		}
	}
	if stopBitsStr != "" {
		if bits, err := strconv.ParseUint(stopBitsStr, 10, 32); err == nil {
			stopBits = uint(bits)
		}
	}
	if minimumReadSizeStr != "" {
		if size, err := strconv.ParseUint(minimumReadSizeStr, 10, 32); err == nil {
			minimumReadSize = uint(size)
		}
	}

	serialConfig := &models.ConnectSerialPort{
		PortName:        portName,
		BaudRate:        baudRate,
		DataBits:        dataBits,
		StopBits:        stopBits,
		MinimumReadSize: minimumReadSize,
	}

	return tm.sessionManager.ConnectToSerialPort(config.RunId, serialConfig)
}

// 保存所有配置到存储
func (tm *TunnelManager) SaveAllConfigs() error {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	configs := make(map[string]*TunnelConfig)
	for id, config := range tm.tunnels {
		// 创建配置副本，避免并发问题
		configCopy := *config
		configCopy.Status = false // 保存时重置状态
		configs[id] = &configCopy
	}

	// 这里使用HTTP代理的存储系统保存配置
	for _, config := range configs {
		if err := tm.saveTunnelConfig(config); err != nil {
			log.Printf("保存穿透配置失败 [%s]: %s", config.ID, err.Error())
		}
	}

	return nil
}

// 从存储加载所有配置
func (tm *TunnelManager) LoadAllConfigs() error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	// 这里使用HTTP代理的存储系统加载配置
	// 加载后重置活跃时间，避免因重启导致的立即清理
	now := time.Now()
	for _, config := range tm.tunnels {
		config.LastActive = now
		config.Status = false // 重置状态，需要重新启动

		// 标记端口为已使用
		if config.ListenPort >= tm.portRangeStart && config.ListenPort <= tm.portRangeEnd {
			tm.usedPorts[config.ListenPort] = true
		}
	}

	log.Printf("穿透配置加载完成，已加载 %d 个配置", len(tm.tunnels))
	return nil
}

// 保存单个配置
func (tm *TunnelManager) saveTunnelConfig(config *TunnelConfig) error {
	// 将配置转换为JSON
	configBytes, err := json.Marshal(config)
	if err != nil {
		return err
	}

	// 这里可以使用Redis或其他存储方式
	// 为了不影响现有代码，暂时使用日志记录
	log.Printf("保存穿透配置: %s -> %s", config.ID, string(configBytes))
	return nil
}

// 停止所有穿透服务
func (tm *TunnelManager) StopAll() {
	tm.mutex.Lock()

	// 发送停止信号
	close(tm.stopChan)

	// 停止所有监听器
	for tunnelID := range tm.tunnels {
		tm.stopTunnelUnsafe(tunnelID)
	}

	tm.mutex.Unlock()

	// 等待所有goroutine完成
	tm.wg.Wait()

	log.Println("所有穿透服务已停止")
}

// 重启穿透服务
func (tm *TunnelManager) RestartTunnel(tunnelID string) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	config, exists := tm.tunnels[tunnelID]
	if !exists {
		return errors.New(fmt.Sprintf("穿透配置不存在: %s", tunnelID))
	}

	// 停止现有服务
	tm.stopTunnelUnsafe(tunnelID)

	// 重新启动服务
	err := tm.startTunnelUnsafe(config)
	if err != nil {
		return err
	}

	config.Status = true
	log.Printf("穿透服务重启成功: %s", tunnelID)
	return nil
}

// 获取穿透服务统计信息
func (tm *TunnelManager) GetTunnelStats() map[string]interface{} {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	stats := make(map[string]interface{})
	totalActive := int64(0)
	totalConnections := int64(0)
	totalBytes := int64(0)
	totalErrors := int64(0)
	typeCount := make(map[string]int)

	for _, config := range tm.tunnels {
		typeCount[config.TunnelType]++
		if config.Status {
			totalActive++
		}
		if config.Metrics != nil {
			totalConnections += atomic.LoadInt64(&config.Metrics.TotalConnections)
			totalBytes += atomic.LoadInt64(&config.Metrics.TotalBytes)
			totalErrors += atomic.LoadInt64(&config.Metrics.ErrorCount)
		}
	}

	stats["totalTunnels"] = len(tm.tunnels)
	stats["activeTunnels"] = totalActive
	stats["totalConnections"] = totalConnections
	stats["totalBytes"] = totalBytes
	stats["totalErrors"] = totalErrors
	stats["tunnelsByType"] = typeCount
	stats["timestamp"] = time.Now()

	return stats
}

// 重置统计信息
func (tm *TunnelManager) ResetStats(tunnelID string) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	config, exists := tm.tunnels[tunnelID]
	if !exists {
		return ErrTunnelNotFound
	}

	config.Metrics = &TunnelMetrics{
		LastErrorTime: time.Now(),
	}

	return nil
}

// 设置不活跃超时时间
func (tm *TunnelManager) SetInactiveTimeout(timeout time.Duration) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	tm.inactiveTimeout = timeout
}

// 启动清理定时器
func (tm *TunnelManager) startCleanupTimer() {
	tm.wg.Add(1)
	go func() {
		defer tm.wg.Done()
		ticker := time.NewTicker(CleanupInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				tm.cleanInactiveTunnels()
			case <-tm.stopChan:
				return
			}
		}
	}()
}

// 清理不活跃的穿透
func (tm *TunnelManager) cleanInactiveTunnels() {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	now := time.Now()
	for id, config := range tm.tunnels {
		if config.Status && !config.LastActive.IsZero() {
			if now.Sub(config.LastActive) > tm.inactiveTimeout {
				log.Printf("清理不活跃穿透 [%s]: 最后活跃时间 %v", id, config.LastActive)

				// 停止服务
				tm.stopTunnelUnsafe(id)

				// 释放端口
				if config.ListenPort >= tm.portRangeStart && config.ListenPort <= tm.portRangeEnd {
					tm.releasePort(config.ListenPort)
				}

				// 删除配置
				delete(tm.tunnels, id)
			}
		}
	}
}

// 更新穿透活跃时间
func (tm *TunnelManager) updateTunnelActivity(tunnelID string) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	if config, exists := tm.tunnels[tunnelID]; exists {
		config.LastActive = time.Now()
	}
}

// 从端口区间分配一个可用端口
func (tm *TunnelManager) allocatePort() (int, error) {
	tm.portRangeMutex.Lock()
	defer tm.portRangeMutex.Unlock()

	// 检查是否还有可用端口
	if len(tm.usedPorts) >= (tm.portRangeEnd - tm.portRangeStart + 1) {
		return 0, fmt.Errorf("没有可用的端口，所有端口（%d-%d）都已被占用", tm.portRangeStart, tm.portRangeEnd)
	}

	// 尝试随机分配端口
	maxAttempts := 100 // 最大尝试次数，避免无限循环
	for i := 0; i < maxAttempts; i++ {
		// 在端口范围内随机选择一个端口
		port := tm.portRangeStart + rand.Intn(tm.portRangeEnd-tm.portRangeStart+1)

		// 检查端口是否已被使用
		if tm.usedPorts[port] {
			continue
		}

		// 检查端口是否被系统占用
		listener, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
		if err != nil {
			// 端口被占用，尝试下一个
			continue
		}
		listener.Close()

		// 标记端口为已使用
		tm.usedPorts[port] = true
		return port, nil
	}

	// 如果随机分配失败，顺序查找可用端口
	for port := tm.portRangeStart; port <= tm.portRangeEnd; port++ {
		if tm.usedPorts[port] {
			continue
		}

		listener, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
		if err != nil {
			continue
		}
		listener.Close()

		tm.usedPorts[port] = true
		return port, nil
	}

	return 0, fmt.Errorf("无法分配端口，所有端口（%d-%d）都已被占用或无法使用", tm.portRangeStart, tm.portRangeEnd)
}

// 释放端口
func (tm *TunnelManager) releasePort(port int) {
	tm.portRangeMutex.Lock()
	defer tm.portRangeMutex.Unlock()

	// 检查端口是否在范围内
	if port >= tm.portRangeStart && port <= tm.portRangeEnd {
		delete(tm.usedPorts, port)
	}
}

// 设置端口区间
func (tm *TunnelManager) SetPortRange(start, end int) error {
	if start >= end {
		return fmt.Errorf("端口区间无效：起始端口必须小于结束端口")
	}

	if start < 1024 || end > 65535 {
		return fmt.Errorf("端口区间无效：端口必须在1024-65535之间")
	}

	tm.portRangeMutex.Lock()
	defer tm.portRangeMutex.Unlock()

	// 更新端口区间
	tm.portRangeStart = start
	tm.portRangeEnd = end

	// 清理不在新区间内的已使用端口记录
	for port := range tm.usedPorts {
		if port < start || port > end {
			delete(tm.usedPorts, port)
		}
	}

	return nil
}
