// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v5.26.1
// source: serverManager.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ServerManagerClient is the client API for ServerManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ServerManagerClient interface {
	// 对服务器的操作
	AddServer(ctx context.Context, in *ServerInfo, opts ...grpc.CallOption) (*OperationResponse, error)
	DelServer(ctx context.Context, in *ServerInfo, opts ...grpc.CallOption) (*OperationResponse, error)
	UpdateServer(ctx context.Context, in *ServerInfo, opts ...grpc.CallOption) (*OperationResponse, error)
	QueryServer(ctx context.Context, in *wrapperspb.StringValue, opts ...grpc.CallOption) (*ServerInfoList, error)
	// 获取所有我可以使用的服务器(公共服务器、自己添加的服务器、别人分享给我的服务器)
	GetAllServer(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ServerInfoList, error)
	// 获取自己作为管理员的所有服务器
	GetAllMyServers(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ServerInfoList, error)
	// 获取别人分享给自己的所有服务器
	GetAllMySharedServers(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ServerInfoList, error)
}

type serverManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewServerManagerClient(cc grpc.ClientConnInterface) ServerManagerClient {
	return &serverManagerClient{cc}
}

func (c *serverManagerClient) AddServer(ctx context.Context, in *ServerInfo, opts ...grpc.CallOption) (*OperationResponse, error) {
	out := new(OperationResponse)
	err := c.cc.Invoke(ctx, "/pb.ServerManager/AddServer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverManagerClient) DelServer(ctx context.Context, in *ServerInfo, opts ...grpc.CallOption) (*OperationResponse, error) {
	out := new(OperationResponse)
	err := c.cc.Invoke(ctx, "/pb.ServerManager/DelServer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverManagerClient) UpdateServer(ctx context.Context, in *ServerInfo, opts ...grpc.CallOption) (*OperationResponse, error) {
	out := new(OperationResponse)
	err := c.cc.Invoke(ctx, "/pb.ServerManager/UpdateServer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverManagerClient) QueryServer(ctx context.Context, in *wrapperspb.StringValue, opts ...grpc.CallOption) (*ServerInfoList, error) {
	out := new(ServerInfoList)
	err := c.cc.Invoke(ctx, "/pb.ServerManager/QueryServer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverManagerClient) GetAllServer(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ServerInfoList, error) {
	out := new(ServerInfoList)
	err := c.cc.Invoke(ctx, "/pb.ServerManager/GetAllServer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverManagerClient) GetAllMyServers(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ServerInfoList, error) {
	out := new(ServerInfoList)
	err := c.cc.Invoke(ctx, "/pb.ServerManager/GetAllMyServers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverManagerClient) GetAllMySharedServers(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*ServerInfoList, error) {
	out := new(ServerInfoList)
	err := c.cc.Invoke(ctx, "/pb.ServerManager/GetAllMySharedServers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServerManagerServer is the server API for ServerManager service.
// All implementations must embed UnimplementedServerManagerServer
// for forward compatibility
type ServerManagerServer interface {
	// 对服务器的操作
	AddServer(context.Context, *ServerInfo) (*OperationResponse, error)
	DelServer(context.Context, *ServerInfo) (*OperationResponse, error)
	UpdateServer(context.Context, *ServerInfo) (*OperationResponse, error)
	QueryServer(context.Context, *wrapperspb.StringValue) (*ServerInfoList, error)
	// 获取所有我可以使用的服务器(公共服务器、自己添加的服务器、别人分享给我的服务器)
	GetAllServer(context.Context, *emptypb.Empty) (*ServerInfoList, error)
	// 获取自己作为管理员的所有服务器
	GetAllMyServers(context.Context, *emptypb.Empty) (*ServerInfoList, error)
	// 获取别人分享给自己的所有服务器
	GetAllMySharedServers(context.Context, *emptypb.Empty) (*ServerInfoList, error)
	mustEmbedUnimplementedServerManagerServer()
}

// UnimplementedServerManagerServer must be embedded to have forward compatible implementations.
type UnimplementedServerManagerServer struct {
}

func (UnimplementedServerManagerServer) AddServer(context.Context, *ServerInfo) (*OperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddServer not implemented")
}
func (UnimplementedServerManagerServer) DelServer(context.Context, *ServerInfo) (*OperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelServer not implemented")
}
func (UnimplementedServerManagerServer) UpdateServer(context.Context, *ServerInfo) (*OperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateServer not implemented")
}
func (UnimplementedServerManagerServer) QueryServer(context.Context, *wrapperspb.StringValue) (*ServerInfoList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryServer not implemented")
}
func (UnimplementedServerManagerServer) GetAllServer(context.Context, *emptypb.Empty) (*ServerInfoList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllServer not implemented")
}
func (UnimplementedServerManagerServer) GetAllMyServers(context.Context, *emptypb.Empty) (*ServerInfoList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllMyServers not implemented")
}
func (UnimplementedServerManagerServer) GetAllMySharedServers(context.Context, *emptypb.Empty) (*ServerInfoList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllMySharedServers not implemented")
}
func (UnimplementedServerManagerServer) mustEmbedUnimplementedServerManagerServer() {}

// UnsafeServerManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServerManagerServer will
// result in compilation errors.
type UnsafeServerManagerServer interface {
	mustEmbedUnimplementedServerManagerServer()
}

func RegisterServerManagerServer(s grpc.ServiceRegistrar, srv ServerManagerServer) {
	s.RegisterService(&ServerManager_ServiceDesc, srv)
}

func _ServerManager_AddServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServerInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerManagerServer).AddServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.ServerManager/AddServer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerManagerServer).AddServer(ctx, req.(*ServerInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerManager_DelServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServerInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerManagerServer).DelServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.ServerManager/DelServer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerManagerServer).DelServer(ctx, req.(*ServerInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerManager_UpdateServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ServerInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerManagerServer).UpdateServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.ServerManager/UpdateServer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerManagerServer).UpdateServer(ctx, req.(*ServerInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerManager_QueryServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wrapperspb.StringValue)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerManagerServer).QueryServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.ServerManager/QueryServer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerManagerServer).QueryServer(ctx, req.(*wrapperspb.StringValue))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerManager_GetAllServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerManagerServer).GetAllServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.ServerManager/GetAllServer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerManagerServer).GetAllServer(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerManager_GetAllMyServers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerManagerServer).GetAllMyServers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.ServerManager/GetAllMyServers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerManagerServer).GetAllMyServers(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ServerManager_GetAllMySharedServers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerManagerServer).GetAllMySharedServers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.ServerManager/GetAllMySharedServers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerManagerServer).GetAllMySharedServers(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// ServerManager_ServiceDesc is the grpc.ServiceDesc for ServerManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ServerManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ServerManager",
	HandlerType: (*ServerManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddServer",
			Handler:    _ServerManager_AddServer_Handler,
		},
		{
			MethodName: "DelServer",
			Handler:    _ServerManager_DelServer_Handler,
		},
		{
			MethodName: "UpdateServer",
			Handler:    _ServerManager_UpdateServer_Handler,
		},
		{
			MethodName: "QueryServer",
			Handler:    _ServerManager_QueryServer_Handler,
		},
		{
			MethodName: "GetAllServer",
			Handler:    _ServerManager_GetAllServer_Handler,
		},
		{
			MethodName: "GetAllMyServers",
			Handler:    _ServerManager_GetAllMyServers_Handler,
		},
		{
			MethodName: "GetAllMySharedServers",
			Handler:    _ServerManager_GetAllMySharedServers_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "serverManager.proto",
}
