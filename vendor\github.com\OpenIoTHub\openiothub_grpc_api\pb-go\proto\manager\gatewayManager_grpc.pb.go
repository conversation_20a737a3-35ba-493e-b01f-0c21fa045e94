// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v5.26.1
// source: gatewayManager.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// GatewayManagerClient is the client API for GatewayManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GatewayManagerClient interface {
	// 对网关的操作
	AddGateway(ctx context.Context, in *GatewayInfo, opts ...grpc.CallOption) (*OperationResponse, error)
	DelGateway(ctx context.Context, in *GatewayInfo, opts ...grpc.CallOption) (*OperationResponse, error)
	UpdateGateway(ctx context.Context, in *GatewayInfo, opts ...grpc.CallOption) (*OperationResponse, error)
	QueryGateway(ctx context.Context, in *GatewayInfo, opts ...grpc.CallOption) (*GatewayInfo, error)
	GetAllGateway(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GatewayInfoList, error)
	UpdateGatewayNameAndDescription(ctx context.Context, in *GatewayInfo, opts ...grpc.CallOption) (*OperationResponse, error)
	GenerateOneGatewayWithDefaultServer(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GatewayInfo, error)
	GenerateOneGatewayWithServerUuid(ctx context.Context, in *wrapperspb.StringValue, opts ...grpc.CallOption) (*GatewayInfo, error)
	GetGatewayJwtByGatewayUuid(ctx context.Context, in *wrapperspb.StringValue, opts ...grpc.CallOption) (*wrapperspb.StringValue, error)
	GetOpenIoTHubJwtByGatewayUuid(ctx context.Context, in *wrapperspb.StringValue, opts ...grpc.CallOption) (*wrapperspb.StringValue, error)
}

type gatewayManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewGatewayManagerClient(cc grpc.ClientConnInterface) GatewayManagerClient {
	return &gatewayManagerClient{cc}
}

func (c *gatewayManagerClient) AddGateway(ctx context.Context, in *GatewayInfo, opts ...grpc.CallOption) (*OperationResponse, error) {
	out := new(OperationResponse)
	err := c.cc.Invoke(ctx, "/pb.GatewayManager/AddGateway", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayManagerClient) DelGateway(ctx context.Context, in *GatewayInfo, opts ...grpc.CallOption) (*OperationResponse, error) {
	out := new(OperationResponse)
	err := c.cc.Invoke(ctx, "/pb.GatewayManager/DelGateway", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayManagerClient) UpdateGateway(ctx context.Context, in *GatewayInfo, opts ...grpc.CallOption) (*OperationResponse, error) {
	out := new(OperationResponse)
	err := c.cc.Invoke(ctx, "/pb.GatewayManager/UpdateGateway", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayManagerClient) QueryGateway(ctx context.Context, in *GatewayInfo, opts ...grpc.CallOption) (*GatewayInfo, error) {
	out := new(GatewayInfo)
	err := c.cc.Invoke(ctx, "/pb.GatewayManager/QueryGateway", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayManagerClient) GetAllGateway(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GatewayInfoList, error) {
	out := new(GatewayInfoList)
	err := c.cc.Invoke(ctx, "/pb.GatewayManager/GetAllGateway", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayManagerClient) UpdateGatewayNameAndDescription(ctx context.Context, in *GatewayInfo, opts ...grpc.CallOption) (*OperationResponse, error) {
	out := new(OperationResponse)
	err := c.cc.Invoke(ctx, "/pb.GatewayManager/UpdateGatewayNameAndDescription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayManagerClient) GenerateOneGatewayWithDefaultServer(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*GatewayInfo, error) {
	out := new(GatewayInfo)
	err := c.cc.Invoke(ctx, "/pb.GatewayManager/GenerateOneGatewayWithDefaultServer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayManagerClient) GenerateOneGatewayWithServerUuid(ctx context.Context, in *wrapperspb.StringValue, opts ...grpc.CallOption) (*GatewayInfo, error) {
	out := new(GatewayInfo)
	err := c.cc.Invoke(ctx, "/pb.GatewayManager/GenerateOneGatewayWithServerUuid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayManagerClient) GetGatewayJwtByGatewayUuid(ctx context.Context, in *wrapperspb.StringValue, opts ...grpc.CallOption) (*wrapperspb.StringValue, error) {
	out := new(wrapperspb.StringValue)
	err := c.cc.Invoke(ctx, "/pb.GatewayManager/GetGatewayJwtByGatewayUuid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gatewayManagerClient) GetOpenIoTHubJwtByGatewayUuid(ctx context.Context, in *wrapperspb.StringValue, opts ...grpc.CallOption) (*wrapperspb.StringValue, error) {
	out := new(wrapperspb.StringValue)
	err := c.cc.Invoke(ctx, "/pb.GatewayManager/GetOpenIoTHubJwtByGatewayUuid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GatewayManagerServer is the server API for GatewayManager service.
// All implementations must embed UnimplementedGatewayManagerServer
// for forward compatibility
type GatewayManagerServer interface {
	// 对网关的操作
	AddGateway(context.Context, *GatewayInfo) (*OperationResponse, error)
	DelGateway(context.Context, *GatewayInfo) (*OperationResponse, error)
	UpdateGateway(context.Context, *GatewayInfo) (*OperationResponse, error)
	QueryGateway(context.Context, *GatewayInfo) (*GatewayInfo, error)
	GetAllGateway(context.Context, *emptypb.Empty) (*GatewayInfoList, error)
	UpdateGatewayNameAndDescription(context.Context, *GatewayInfo) (*OperationResponse, error)
	GenerateOneGatewayWithDefaultServer(context.Context, *emptypb.Empty) (*GatewayInfo, error)
	GenerateOneGatewayWithServerUuid(context.Context, *wrapperspb.StringValue) (*GatewayInfo, error)
	GetGatewayJwtByGatewayUuid(context.Context, *wrapperspb.StringValue) (*wrapperspb.StringValue, error)
	GetOpenIoTHubJwtByGatewayUuid(context.Context, *wrapperspb.StringValue) (*wrapperspb.StringValue, error)
	mustEmbedUnimplementedGatewayManagerServer()
}

// UnimplementedGatewayManagerServer must be embedded to have forward compatible implementations.
type UnimplementedGatewayManagerServer struct {
}

func (UnimplementedGatewayManagerServer) AddGateway(context.Context, *GatewayInfo) (*OperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddGateway not implemented")
}
func (UnimplementedGatewayManagerServer) DelGateway(context.Context, *GatewayInfo) (*OperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelGateway not implemented")
}
func (UnimplementedGatewayManagerServer) UpdateGateway(context.Context, *GatewayInfo) (*OperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGateway not implemented")
}
func (UnimplementedGatewayManagerServer) QueryGateway(context.Context, *GatewayInfo) (*GatewayInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryGateway not implemented")
}
func (UnimplementedGatewayManagerServer) GetAllGateway(context.Context, *emptypb.Empty) (*GatewayInfoList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllGateway not implemented")
}
func (UnimplementedGatewayManagerServer) UpdateGatewayNameAndDescription(context.Context, *GatewayInfo) (*OperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGatewayNameAndDescription not implemented")
}
func (UnimplementedGatewayManagerServer) GenerateOneGatewayWithDefaultServer(context.Context, *emptypb.Empty) (*GatewayInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateOneGatewayWithDefaultServer not implemented")
}
func (UnimplementedGatewayManagerServer) GenerateOneGatewayWithServerUuid(context.Context, *wrapperspb.StringValue) (*GatewayInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateOneGatewayWithServerUuid not implemented")
}
func (UnimplementedGatewayManagerServer) GetGatewayJwtByGatewayUuid(context.Context, *wrapperspb.StringValue) (*wrapperspb.StringValue, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGatewayJwtByGatewayUuid not implemented")
}
func (UnimplementedGatewayManagerServer) GetOpenIoTHubJwtByGatewayUuid(context.Context, *wrapperspb.StringValue) (*wrapperspb.StringValue, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOpenIoTHubJwtByGatewayUuid not implemented")
}
func (UnimplementedGatewayManagerServer) mustEmbedUnimplementedGatewayManagerServer() {}

// UnsafeGatewayManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GatewayManagerServer will
// result in compilation errors.
type UnsafeGatewayManagerServer interface {
	mustEmbedUnimplementedGatewayManagerServer()
}

func RegisterGatewayManagerServer(s grpc.ServiceRegistrar, srv GatewayManagerServer) {
	s.RegisterService(&GatewayManager_ServiceDesc, srv)
}

func _GatewayManager_AddGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GatewayInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayManagerServer).AddGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.GatewayManager/AddGateway",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayManagerServer).AddGateway(ctx, req.(*GatewayInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayManager_DelGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GatewayInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayManagerServer).DelGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.GatewayManager/DelGateway",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayManagerServer).DelGateway(ctx, req.(*GatewayInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayManager_UpdateGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GatewayInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayManagerServer).UpdateGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.GatewayManager/UpdateGateway",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayManagerServer).UpdateGateway(ctx, req.(*GatewayInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayManager_QueryGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GatewayInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayManagerServer).QueryGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.GatewayManager/QueryGateway",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayManagerServer).QueryGateway(ctx, req.(*GatewayInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayManager_GetAllGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayManagerServer).GetAllGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.GatewayManager/GetAllGateway",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayManagerServer).GetAllGateway(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayManager_UpdateGatewayNameAndDescription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GatewayInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayManagerServer).UpdateGatewayNameAndDescription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.GatewayManager/UpdateGatewayNameAndDescription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayManagerServer).UpdateGatewayNameAndDescription(ctx, req.(*GatewayInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayManager_GenerateOneGatewayWithDefaultServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayManagerServer).GenerateOneGatewayWithDefaultServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.GatewayManager/GenerateOneGatewayWithDefaultServer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayManagerServer).GenerateOneGatewayWithDefaultServer(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayManager_GenerateOneGatewayWithServerUuid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wrapperspb.StringValue)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayManagerServer).GenerateOneGatewayWithServerUuid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.GatewayManager/GenerateOneGatewayWithServerUuid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayManagerServer).GenerateOneGatewayWithServerUuid(ctx, req.(*wrapperspb.StringValue))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayManager_GetGatewayJwtByGatewayUuid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wrapperspb.StringValue)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayManagerServer).GetGatewayJwtByGatewayUuid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.GatewayManager/GetGatewayJwtByGatewayUuid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayManagerServer).GetGatewayJwtByGatewayUuid(ctx, req.(*wrapperspb.StringValue))
	}
	return interceptor(ctx, in, info, handler)
}

func _GatewayManager_GetOpenIoTHubJwtByGatewayUuid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wrapperspb.StringValue)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GatewayManagerServer).GetOpenIoTHubJwtByGatewayUuid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.GatewayManager/GetOpenIoTHubJwtByGatewayUuid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GatewayManagerServer).GetOpenIoTHubJwtByGatewayUuid(ctx, req.(*wrapperspb.StringValue))
	}
	return interceptor(ctx, in, info, handler)
}

// GatewayManager_ServiceDesc is the grpc.ServiceDesc for GatewayManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GatewayManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.GatewayManager",
	HandlerType: (*GatewayManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddGateway",
			Handler:    _GatewayManager_AddGateway_Handler,
		},
		{
			MethodName: "DelGateway",
			Handler:    _GatewayManager_DelGateway_Handler,
		},
		{
			MethodName: "UpdateGateway",
			Handler:    _GatewayManager_UpdateGateway_Handler,
		},
		{
			MethodName: "QueryGateway",
			Handler:    _GatewayManager_QueryGateway_Handler,
		},
		{
			MethodName: "GetAllGateway",
			Handler:    _GatewayManager_GetAllGateway_Handler,
		},
		{
			MethodName: "UpdateGatewayNameAndDescription",
			Handler:    _GatewayManager_UpdateGatewayNameAndDescription_Handler,
		},
		{
			MethodName: "GenerateOneGatewayWithDefaultServer",
			Handler:    _GatewayManager_GenerateOneGatewayWithDefaultServer_Handler,
		},
		{
			MethodName: "GenerateOneGatewayWithServerUuid",
			Handler:    _GatewayManager_GenerateOneGatewayWithServerUuid_Handler,
		},
		{
			MethodName: "GetGatewayJwtByGatewayUuid",
			Handler:    _GatewayManager_GetGatewayJwtByGatewayUuid_Handler,
		},
		{
			MethodName: "GetOpenIoTHubJwtByGatewayUuid",
			Handler:    _GatewayManager_GetOpenIoTHubJwtByGatewayUuid_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gatewayManager.proto",
}
