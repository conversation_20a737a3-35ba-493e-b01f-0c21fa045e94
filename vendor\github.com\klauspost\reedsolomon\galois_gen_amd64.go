// Code generated by command: go run gen.go -out ../galois_gen_amd64.s -stubs ../galois_gen_amd64.go -pkg=reedsolomon. DO NOT EDIT.

//go:build !appengine && !noasm && !nogen && !nopshufb && gc

package reedsolomon

func _dummy_()

//go:noescape
func sSE2XorSlice(in []byte, out []byte)

//go:noescape
func sSE2XorSlice_64(in []byte, out []byte)

//go:noescape
func avx2XorSlice_64(in []byte, out []byte)

// mulAvxTwo_1x1_64 takes 1 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_1x1_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x1_64 takes 1 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_1x1_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x1 takes 1 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_1x1(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x1_64Xor takes 1 inputs and produces 1 outputs.
//
//go:noescape
func mulGFNI_1x1_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x1Xor takes 1 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxGFNI_1x1Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x1_64Xor takes 1 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxTwo_1x1_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x2_64 takes 1 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_1x2_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x2_64 takes 1 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_1x2_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x2 takes 1 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_1x2(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x2_64Xor takes 1 inputs and produces 2 outputs.
//
//go:noescape
func mulGFNI_1x2_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x2Xor takes 1 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxGFNI_1x2Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x2_64Xor takes 1 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxTwo_1x2_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x3_64 takes 1 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_1x3_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x3_64 takes 1 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_1x3_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x3 takes 1 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_1x3(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x3_64Xor takes 1 inputs and produces 3 outputs.
//
//go:noescape
func mulGFNI_1x3_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x3Xor takes 1 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxGFNI_1x3Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x3_64Xor takes 1 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxTwo_1x3_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x4 takes 1 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_1x4(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x4_64 takes 1 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_1x4_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x4 takes 1 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_1x4(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x4_64Xor takes 1 inputs and produces 4 outputs.
//
//go:noescape
func mulGFNI_1x4_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x4Xor takes 1 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxGFNI_1x4Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x4Xor takes 1 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxTwo_1x4Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x5 takes 1 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_1x5(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x5_64 takes 1 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_1x5_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x5 takes 1 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_1x5(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x5_64Xor takes 1 inputs and produces 5 outputs.
//
//go:noescape
func mulGFNI_1x5_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x5Xor takes 1 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxGFNI_1x5Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x5Xor takes 1 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxTwo_1x5Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x6 takes 1 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_1x6(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x6_64 takes 1 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_1x6_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x6 takes 1 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_1x6(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x6_64Xor takes 1 inputs and produces 6 outputs.
//
//go:noescape
func mulGFNI_1x6_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x6Xor takes 1 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxGFNI_1x6Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x6Xor takes 1 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxTwo_1x6Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x7 takes 1 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_1x7(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x7_64 takes 1 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_1x7_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x7 takes 1 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_1x7(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x7_64Xor takes 1 inputs and produces 7 outputs.
//
//go:noescape
func mulGFNI_1x7_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x7Xor takes 1 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxGFNI_1x7Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x7Xor takes 1 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxTwo_1x7Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x8 takes 1 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_1x8(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x8_64 takes 1 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_1x8_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x8 takes 1 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_1x8(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x8_64Xor takes 1 inputs and produces 8 outputs.
//
//go:noescape
func mulGFNI_1x8_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x8Xor takes 1 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxGFNI_1x8Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x8Xor takes 1 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxTwo_1x8Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x9 takes 1 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_1x9(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x9_64 takes 1 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_1x9_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x9 takes 1 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_1x9(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x9_64Xor takes 1 inputs and produces 9 outputs.
//
//go:noescape
func mulGFNI_1x9_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x9Xor takes 1 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxGFNI_1x9Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x9Xor takes 1 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxTwo_1x9Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x10 takes 1 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_1x10(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x10_64 takes 1 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_1x10_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x10 takes 1 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_1x10(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_1x10_64Xor takes 1 inputs and produces 10 outputs.
//
//go:noescape
func mulGFNI_1x10_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_1x10Xor takes 1 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxGFNI_1x10Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_1x10Xor takes 1 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxTwo_1x10Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x1_64 takes 2 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_2x1_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x1_64 takes 2 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_2x1_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x1 takes 2 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_2x1(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x1_64Xor takes 2 inputs and produces 1 outputs.
//
//go:noescape
func mulGFNI_2x1_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x1Xor takes 2 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxGFNI_2x1Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x1_64Xor takes 2 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxTwo_2x1_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x2_64 takes 2 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_2x2_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x2_64 takes 2 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_2x2_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x2 takes 2 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_2x2(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x2_64Xor takes 2 inputs and produces 2 outputs.
//
//go:noescape
func mulGFNI_2x2_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x2Xor takes 2 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxGFNI_2x2Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x2_64Xor takes 2 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxTwo_2x2_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x3_64 takes 2 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_2x3_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x3_64 takes 2 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_2x3_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x3 takes 2 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_2x3(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x3_64Xor takes 2 inputs and produces 3 outputs.
//
//go:noescape
func mulGFNI_2x3_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x3Xor takes 2 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxGFNI_2x3Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x3_64Xor takes 2 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxTwo_2x3_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x4 takes 2 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_2x4(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x4_64 takes 2 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_2x4_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x4 takes 2 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_2x4(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x4_64Xor takes 2 inputs and produces 4 outputs.
//
//go:noescape
func mulGFNI_2x4_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x4Xor takes 2 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxGFNI_2x4Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x4Xor takes 2 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxTwo_2x4Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x5 takes 2 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_2x5(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x5_64 takes 2 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_2x5_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x5 takes 2 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_2x5(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x5_64Xor takes 2 inputs and produces 5 outputs.
//
//go:noescape
func mulGFNI_2x5_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x5Xor takes 2 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxGFNI_2x5Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x5Xor takes 2 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxTwo_2x5Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x6 takes 2 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_2x6(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x6_64 takes 2 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_2x6_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x6 takes 2 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_2x6(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x6_64Xor takes 2 inputs and produces 6 outputs.
//
//go:noescape
func mulGFNI_2x6_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x6Xor takes 2 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxGFNI_2x6Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x6Xor takes 2 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxTwo_2x6Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x7 takes 2 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_2x7(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x7_64 takes 2 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_2x7_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x7 takes 2 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_2x7(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x7_64Xor takes 2 inputs and produces 7 outputs.
//
//go:noescape
func mulGFNI_2x7_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x7Xor takes 2 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxGFNI_2x7Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x7Xor takes 2 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxTwo_2x7Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x8 takes 2 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_2x8(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x8_64 takes 2 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_2x8_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x8 takes 2 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_2x8(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x8_64Xor takes 2 inputs and produces 8 outputs.
//
//go:noescape
func mulGFNI_2x8_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x8Xor takes 2 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxGFNI_2x8Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x8Xor takes 2 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxTwo_2x8Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x9 takes 2 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_2x9(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x9_64 takes 2 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_2x9_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x9 takes 2 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_2x9(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x9_64Xor takes 2 inputs and produces 9 outputs.
//
//go:noescape
func mulGFNI_2x9_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x9Xor takes 2 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxGFNI_2x9Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x9Xor takes 2 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxTwo_2x9Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x10 takes 2 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_2x10(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x10_64 takes 2 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_2x10_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x10 takes 2 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_2x10(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_2x10_64Xor takes 2 inputs and produces 10 outputs.
//
//go:noescape
func mulGFNI_2x10_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_2x10Xor takes 2 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxGFNI_2x10Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_2x10Xor takes 2 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxTwo_2x10Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x1_64 takes 3 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_3x1_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x1_64 takes 3 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_3x1_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x1 takes 3 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_3x1(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x1_64Xor takes 3 inputs and produces 1 outputs.
//
//go:noescape
func mulGFNI_3x1_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x1Xor takes 3 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxGFNI_3x1Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x1_64Xor takes 3 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxTwo_3x1_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x2_64 takes 3 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_3x2_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x2_64 takes 3 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_3x2_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x2 takes 3 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_3x2(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x2_64Xor takes 3 inputs and produces 2 outputs.
//
//go:noescape
func mulGFNI_3x2_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x2Xor takes 3 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxGFNI_3x2Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x2_64Xor takes 3 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxTwo_3x2_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x3_64 takes 3 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_3x3_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x3_64 takes 3 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_3x3_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x3 takes 3 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_3x3(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x3_64Xor takes 3 inputs and produces 3 outputs.
//
//go:noescape
func mulGFNI_3x3_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x3Xor takes 3 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxGFNI_3x3Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x3_64Xor takes 3 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxTwo_3x3_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x4 takes 3 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_3x4(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x4_64 takes 3 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_3x4_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x4 takes 3 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_3x4(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x4_64Xor takes 3 inputs and produces 4 outputs.
//
//go:noescape
func mulGFNI_3x4_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x4Xor takes 3 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxGFNI_3x4Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x4Xor takes 3 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxTwo_3x4Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x5 takes 3 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_3x5(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x5_64 takes 3 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_3x5_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x5 takes 3 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_3x5(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x5_64Xor takes 3 inputs and produces 5 outputs.
//
//go:noescape
func mulGFNI_3x5_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x5Xor takes 3 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxGFNI_3x5Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x5Xor takes 3 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxTwo_3x5Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x6 takes 3 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_3x6(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x6_64 takes 3 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_3x6_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x6 takes 3 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_3x6(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x6_64Xor takes 3 inputs and produces 6 outputs.
//
//go:noescape
func mulGFNI_3x6_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x6Xor takes 3 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxGFNI_3x6Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x6Xor takes 3 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxTwo_3x6Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x7 takes 3 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_3x7(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x7_64 takes 3 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_3x7_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x7 takes 3 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_3x7(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x7_64Xor takes 3 inputs and produces 7 outputs.
//
//go:noescape
func mulGFNI_3x7_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x7Xor takes 3 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxGFNI_3x7Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x7Xor takes 3 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxTwo_3x7Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x8 takes 3 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_3x8(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x8_64 takes 3 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_3x8_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x8 takes 3 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_3x8(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x8_64Xor takes 3 inputs and produces 8 outputs.
//
//go:noescape
func mulGFNI_3x8_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x8Xor takes 3 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxGFNI_3x8Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x8Xor takes 3 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxTwo_3x8Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x9 takes 3 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_3x9(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x9_64 takes 3 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_3x9_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x9 takes 3 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_3x9(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x9_64Xor takes 3 inputs and produces 9 outputs.
//
//go:noescape
func mulGFNI_3x9_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x9Xor takes 3 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxGFNI_3x9Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x9Xor takes 3 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxTwo_3x9Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x10 takes 3 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_3x10(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x10_64 takes 3 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_3x10_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x10 takes 3 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_3x10(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_3x10_64Xor takes 3 inputs and produces 10 outputs.
//
//go:noescape
func mulGFNI_3x10_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_3x10Xor takes 3 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxGFNI_3x10Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_3x10Xor takes 3 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxTwo_3x10Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x1_64 takes 4 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_4x1_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x1_64 takes 4 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_4x1_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x1 takes 4 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_4x1(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x1_64Xor takes 4 inputs and produces 1 outputs.
//
//go:noescape
func mulGFNI_4x1_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x1Xor takes 4 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxGFNI_4x1Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x1_64Xor takes 4 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxTwo_4x1_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x2_64 takes 4 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_4x2_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x2_64 takes 4 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_4x2_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x2 takes 4 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_4x2(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x2_64Xor takes 4 inputs and produces 2 outputs.
//
//go:noescape
func mulGFNI_4x2_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x2Xor takes 4 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxGFNI_4x2Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x2_64Xor takes 4 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxTwo_4x2_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x3_64 takes 4 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_4x3_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x3_64 takes 4 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_4x3_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x3 takes 4 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_4x3(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x3_64Xor takes 4 inputs and produces 3 outputs.
//
//go:noescape
func mulGFNI_4x3_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x3Xor takes 4 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxGFNI_4x3Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x3_64Xor takes 4 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxTwo_4x3_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x4 takes 4 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_4x4(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x4_64 takes 4 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_4x4_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x4 takes 4 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_4x4(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x4_64Xor takes 4 inputs and produces 4 outputs.
//
//go:noescape
func mulGFNI_4x4_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x4Xor takes 4 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxGFNI_4x4Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x4Xor takes 4 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxTwo_4x4Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x5 takes 4 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_4x5(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x5_64 takes 4 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_4x5_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x5 takes 4 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_4x5(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x5_64Xor takes 4 inputs and produces 5 outputs.
//
//go:noescape
func mulGFNI_4x5_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x5Xor takes 4 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxGFNI_4x5Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x5Xor takes 4 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxTwo_4x5Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x6 takes 4 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_4x6(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x6_64 takes 4 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_4x6_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x6 takes 4 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_4x6(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x6_64Xor takes 4 inputs and produces 6 outputs.
//
//go:noescape
func mulGFNI_4x6_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x6Xor takes 4 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxGFNI_4x6Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x6Xor takes 4 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxTwo_4x6Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x7 takes 4 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_4x7(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x7_64 takes 4 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_4x7_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x7 takes 4 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_4x7(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x7_64Xor takes 4 inputs and produces 7 outputs.
//
//go:noescape
func mulGFNI_4x7_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x7Xor takes 4 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxGFNI_4x7Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x7Xor takes 4 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxTwo_4x7Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x8 takes 4 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_4x8(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x8_64 takes 4 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_4x8_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x8 takes 4 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_4x8(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x8_64Xor takes 4 inputs and produces 8 outputs.
//
//go:noescape
func mulGFNI_4x8_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x8Xor takes 4 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxGFNI_4x8Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x8Xor takes 4 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxTwo_4x8Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x9 takes 4 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_4x9(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x9_64 takes 4 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_4x9_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x9 takes 4 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_4x9(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x9_64Xor takes 4 inputs and produces 9 outputs.
//
//go:noescape
func mulGFNI_4x9_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x9Xor takes 4 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxGFNI_4x9Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x9Xor takes 4 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxTwo_4x9Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x10 takes 4 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_4x10(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x10_64 takes 4 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_4x10_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x10 takes 4 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_4x10(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_4x10_64Xor takes 4 inputs and produces 10 outputs.
//
//go:noescape
func mulGFNI_4x10_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_4x10Xor takes 4 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxGFNI_4x10Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_4x10Xor takes 4 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxTwo_4x10Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x1_64 takes 5 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_5x1_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x1_64 takes 5 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_5x1_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x1 takes 5 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_5x1(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x1_64Xor takes 5 inputs and produces 1 outputs.
//
//go:noescape
func mulGFNI_5x1_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x1Xor takes 5 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxGFNI_5x1Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x1_64Xor takes 5 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxTwo_5x1_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x2_64 takes 5 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_5x2_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x2_64 takes 5 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_5x2_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x2 takes 5 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_5x2(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x2_64Xor takes 5 inputs and produces 2 outputs.
//
//go:noescape
func mulGFNI_5x2_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x2Xor takes 5 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxGFNI_5x2Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x2_64Xor takes 5 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxTwo_5x2_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x3_64 takes 5 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_5x3_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x3_64 takes 5 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_5x3_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x3 takes 5 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_5x3(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x3_64Xor takes 5 inputs and produces 3 outputs.
//
//go:noescape
func mulGFNI_5x3_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x3Xor takes 5 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxGFNI_5x3Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x3_64Xor takes 5 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxTwo_5x3_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x4 takes 5 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_5x4(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x4_64 takes 5 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_5x4_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x4 takes 5 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_5x4(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x4_64Xor takes 5 inputs and produces 4 outputs.
//
//go:noescape
func mulGFNI_5x4_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x4Xor takes 5 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxGFNI_5x4Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x4Xor takes 5 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxTwo_5x4Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x5 takes 5 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_5x5(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x5_64 takes 5 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_5x5_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x5 takes 5 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_5x5(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x5_64Xor takes 5 inputs and produces 5 outputs.
//
//go:noescape
func mulGFNI_5x5_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x5Xor takes 5 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxGFNI_5x5Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x5Xor takes 5 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxTwo_5x5Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x6 takes 5 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_5x6(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x6_64 takes 5 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_5x6_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x6 takes 5 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_5x6(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x6_64Xor takes 5 inputs and produces 6 outputs.
//
//go:noescape
func mulGFNI_5x6_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x6Xor takes 5 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxGFNI_5x6Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x6Xor takes 5 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxTwo_5x6Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x7 takes 5 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_5x7(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x7_64 takes 5 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_5x7_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x7 takes 5 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_5x7(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x7_64Xor takes 5 inputs and produces 7 outputs.
//
//go:noescape
func mulGFNI_5x7_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x7Xor takes 5 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxGFNI_5x7Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x7Xor takes 5 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxTwo_5x7Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x8 takes 5 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_5x8(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x8_64 takes 5 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_5x8_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x8 takes 5 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_5x8(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x8_64Xor takes 5 inputs and produces 8 outputs.
//
//go:noescape
func mulGFNI_5x8_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x8Xor takes 5 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxGFNI_5x8Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x8Xor takes 5 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxTwo_5x8Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x9 takes 5 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_5x9(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x9_64 takes 5 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_5x9_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x9 takes 5 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_5x9(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x9_64Xor takes 5 inputs and produces 9 outputs.
//
//go:noescape
func mulGFNI_5x9_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x9Xor takes 5 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxGFNI_5x9Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x9Xor takes 5 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxTwo_5x9Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x10 takes 5 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_5x10(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x10_64 takes 5 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_5x10_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x10 takes 5 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_5x10(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_5x10_64Xor takes 5 inputs and produces 10 outputs.
//
//go:noescape
func mulGFNI_5x10_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_5x10Xor takes 5 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxGFNI_5x10Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_5x10Xor takes 5 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxTwo_5x10Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x1_64 takes 6 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_6x1_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x1_64 takes 6 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_6x1_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x1 takes 6 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_6x1(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x1_64Xor takes 6 inputs and produces 1 outputs.
//
//go:noescape
func mulGFNI_6x1_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x1Xor takes 6 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxGFNI_6x1Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x1_64Xor takes 6 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxTwo_6x1_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x2_64 takes 6 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_6x2_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x2_64 takes 6 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_6x2_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x2 takes 6 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_6x2(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x2_64Xor takes 6 inputs and produces 2 outputs.
//
//go:noescape
func mulGFNI_6x2_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x2Xor takes 6 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxGFNI_6x2Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x2_64Xor takes 6 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxTwo_6x2_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x3_64 takes 6 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_6x3_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x3_64 takes 6 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_6x3_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x3 takes 6 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_6x3(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x3_64Xor takes 6 inputs and produces 3 outputs.
//
//go:noescape
func mulGFNI_6x3_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x3Xor takes 6 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxGFNI_6x3Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x3_64Xor takes 6 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxTwo_6x3_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x4 takes 6 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_6x4(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x4_64 takes 6 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_6x4_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x4 takes 6 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_6x4(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x4_64Xor takes 6 inputs and produces 4 outputs.
//
//go:noescape
func mulGFNI_6x4_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x4Xor takes 6 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxGFNI_6x4Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x4Xor takes 6 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxTwo_6x4Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x5 takes 6 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_6x5(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x5_64 takes 6 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_6x5_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x5 takes 6 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_6x5(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x5_64Xor takes 6 inputs and produces 5 outputs.
//
//go:noescape
func mulGFNI_6x5_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x5Xor takes 6 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxGFNI_6x5Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x5Xor takes 6 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxTwo_6x5Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x6 takes 6 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_6x6(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x6_64 takes 6 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_6x6_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x6 takes 6 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_6x6(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x6_64Xor takes 6 inputs and produces 6 outputs.
//
//go:noescape
func mulGFNI_6x6_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x6Xor takes 6 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxGFNI_6x6Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x6Xor takes 6 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxTwo_6x6Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x7 takes 6 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_6x7(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x7_64 takes 6 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_6x7_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x7 takes 6 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_6x7(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x7_64Xor takes 6 inputs and produces 7 outputs.
//
//go:noescape
func mulGFNI_6x7_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x7Xor takes 6 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxGFNI_6x7Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x7Xor takes 6 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxTwo_6x7Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x8 takes 6 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_6x8(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x8_64 takes 6 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_6x8_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x8 takes 6 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_6x8(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x8_64Xor takes 6 inputs and produces 8 outputs.
//
//go:noescape
func mulGFNI_6x8_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x8Xor takes 6 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxGFNI_6x8Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x8Xor takes 6 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxTwo_6x8Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x9 takes 6 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_6x9(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x9_64 takes 6 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_6x9_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x9 takes 6 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_6x9(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x9_64Xor takes 6 inputs and produces 9 outputs.
//
//go:noescape
func mulGFNI_6x9_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x9Xor takes 6 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxGFNI_6x9Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x9Xor takes 6 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxTwo_6x9Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x10 takes 6 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_6x10(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x10_64 takes 6 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_6x10_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x10 takes 6 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_6x10(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_6x10_64Xor takes 6 inputs and produces 10 outputs.
//
//go:noescape
func mulGFNI_6x10_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_6x10Xor takes 6 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxGFNI_6x10Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_6x10Xor takes 6 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxTwo_6x10Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x1_64 takes 7 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_7x1_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x1_64 takes 7 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_7x1_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x1 takes 7 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_7x1(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x1_64Xor takes 7 inputs and produces 1 outputs.
//
//go:noescape
func mulGFNI_7x1_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x1Xor takes 7 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxGFNI_7x1Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x1_64Xor takes 7 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxTwo_7x1_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x2_64 takes 7 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_7x2_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x2_64 takes 7 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_7x2_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x2 takes 7 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_7x2(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x2_64Xor takes 7 inputs and produces 2 outputs.
//
//go:noescape
func mulGFNI_7x2_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x2Xor takes 7 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxGFNI_7x2Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x2_64Xor takes 7 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxTwo_7x2_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x3_64 takes 7 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_7x3_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x3_64 takes 7 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_7x3_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x3 takes 7 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_7x3(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x3_64Xor takes 7 inputs and produces 3 outputs.
//
//go:noescape
func mulGFNI_7x3_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x3Xor takes 7 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxGFNI_7x3Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x3_64Xor takes 7 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxTwo_7x3_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x4 takes 7 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_7x4(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x4_64 takes 7 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_7x4_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x4 takes 7 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_7x4(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x4_64Xor takes 7 inputs and produces 4 outputs.
//
//go:noescape
func mulGFNI_7x4_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x4Xor takes 7 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxGFNI_7x4Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x4Xor takes 7 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxTwo_7x4Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x5 takes 7 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_7x5(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x5_64 takes 7 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_7x5_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x5 takes 7 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_7x5(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x5_64Xor takes 7 inputs and produces 5 outputs.
//
//go:noescape
func mulGFNI_7x5_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x5Xor takes 7 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxGFNI_7x5Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x5Xor takes 7 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxTwo_7x5Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x6 takes 7 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_7x6(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x6_64 takes 7 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_7x6_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x6 takes 7 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_7x6(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x6_64Xor takes 7 inputs and produces 6 outputs.
//
//go:noescape
func mulGFNI_7x6_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x6Xor takes 7 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxGFNI_7x6Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x6Xor takes 7 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxTwo_7x6Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x7 takes 7 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_7x7(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x7_64 takes 7 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_7x7_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x7 takes 7 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_7x7(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x7_64Xor takes 7 inputs and produces 7 outputs.
//
//go:noescape
func mulGFNI_7x7_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x7Xor takes 7 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxGFNI_7x7Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x7Xor takes 7 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxTwo_7x7Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x8 takes 7 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_7x8(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x8_64 takes 7 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_7x8_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x8 takes 7 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_7x8(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x8_64Xor takes 7 inputs and produces 8 outputs.
//
//go:noescape
func mulGFNI_7x8_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x8Xor takes 7 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxGFNI_7x8Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x8Xor takes 7 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxTwo_7x8Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x9 takes 7 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_7x9(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x9_64 takes 7 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_7x9_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x9 takes 7 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_7x9(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x9_64Xor takes 7 inputs and produces 9 outputs.
//
//go:noescape
func mulGFNI_7x9_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x9Xor takes 7 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxGFNI_7x9Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x9Xor takes 7 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxTwo_7x9Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x10 takes 7 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_7x10(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x10_64 takes 7 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_7x10_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x10 takes 7 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_7x10(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_7x10_64Xor takes 7 inputs and produces 10 outputs.
//
//go:noescape
func mulGFNI_7x10_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_7x10Xor takes 7 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxGFNI_7x10Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_7x10Xor takes 7 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxTwo_7x10Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x1_64 takes 8 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_8x1_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x1_64 takes 8 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_8x1_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x1 takes 8 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_8x1(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x1_64Xor takes 8 inputs and produces 1 outputs.
//
//go:noescape
func mulGFNI_8x1_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x1Xor takes 8 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxGFNI_8x1Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x1_64Xor takes 8 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxTwo_8x1_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x2_64 takes 8 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_8x2_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x2_64 takes 8 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_8x2_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x2 takes 8 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_8x2(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x2_64Xor takes 8 inputs and produces 2 outputs.
//
//go:noescape
func mulGFNI_8x2_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x2Xor takes 8 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxGFNI_8x2Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x2_64Xor takes 8 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxTwo_8x2_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x3_64 takes 8 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_8x3_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x3_64 takes 8 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_8x3_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x3 takes 8 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_8x3(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x3_64Xor takes 8 inputs and produces 3 outputs.
//
//go:noescape
func mulGFNI_8x3_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x3Xor takes 8 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxGFNI_8x3Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x3_64Xor takes 8 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxTwo_8x3_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x4 takes 8 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_8x4(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x4_64 takes 8 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_8x4_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x4 takes 8 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_8x4(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x4_64Xor takes 8 inputs and produces 4 outputs.
//
//go:noescape
func mulGFNI_8x4_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x4Xor takes 8 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxGFNI_8x4Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x4Xor takes 8 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxTwo_8x4Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x5 takes 8 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_8x5(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x5_64 takes 8 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_8x5_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x5 takes 8 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_8x5(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x5_64Xor takes 8 inputs and produces 5 outputs.
//
//go:noescape
func mulGFNI_8x5_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x5Xor takes 8 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxGFNI_8x5Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x5Xor takes 8 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxTwo_8x5Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x6 takes 8 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_8x6(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x6_64 takes 8 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_8x6_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x6 takes 8 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_8x6(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x6_64Xor takes 8 inputs and produces 6 outputs.
//
//go:noescape
func mulGFNI_8x6_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x6Xor takes 8 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxGFNI_8x6Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x6Xor takes 8 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxTwo_8x6Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x7 takes 8 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_8x7(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x7_64 takes 8 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_8x7_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x7 takes 8 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_8x7(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x7_64Xor takes 8 inputs and produces 7 outputs.
//
//go:noescape
func mulGFNI_8x7_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x7Xor takes 8 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxGFNI_8x7Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x7Xor takes 8 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxTwo_8x7Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x8 takes 8 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_8x8(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x8_64 takes 8 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_8x8_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x8 takes 8 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_8x8(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x8_64Xor takes 8 inputs and produces 8 outputs.
//
//go:noescape
func mulGFNI_8x8_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x8Xor takes 8 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxGFNI_8x8Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x8Xor takes 8 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxTwo_8x8Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x9 takes 8 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_8x9(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x9_64 takes 8 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_8x9_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x9 takes 8 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_8x9(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x9_64Xor takes 8 inputs and produces 9 outputs.
//
//go:noescape
func mulGFNI_8x9_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x9Xor takes 8 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxGFNI_8x9Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x9Xor takes 8 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxTwo_8x9Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x10 takes 8 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_8x10(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x10_64 takes 8 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_8x10_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x10 takes 8 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_8x10(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_8x10_64Xor takes 8 inputs and produces 10 outputs.
//
//go:noescape
func mulGFNI_8x10_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_8x10Xor takes 8 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxGFNI_8x10Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_8x10Xor takes 8 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxTwo_8x10Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x1_64 takes 9 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_9x1_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x1_64 takes 9 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_9x1_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x1 takes 9 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_9x1(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x1_64Xor takes 9 inputs and produces 1 outputs.
//
//go:noescape
func mulGFNI_9x1_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x1Xor takes 9 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxGFNI_9x1Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x1_64Xor takes 9 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxTwo_9x1_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x2_64 takes 9 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_9x2_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x2_64 takes 9 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_9x2_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x2 takes 9 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_9x2(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x2_64Xor takes 9 inputs and produces 2 outputs.
//
//go:noescape
func mulGFNI_9x2_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x2Xor takes 9 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxGFNI_9x2Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x2_64Xor takes 9 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxTwo_9x2_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x3_64 takes 9 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_9x3_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x3_64 takes 9 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_9x3_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x3 takes 9 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_9x3(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x3_64Xor takes 9 inputs and produces 3 outputs.
//
//go:noescape
func mulGFNI_9x3_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x3Xor takes 9 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxGFNI_9x3Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x3_64Xor takes 9 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxTwo_9x3_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x4 takes 9 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_9x4(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x4_64 takes 9 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_9x4_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x4 takes 9 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_9x4(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x4_64Xor takes 9 inputs and produces 4 outputs.
//
//go:noescape
func mulGFNI_9x4_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x4Xor takes 9 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxGFNI_9x4Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x4Xor takes 9 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxTwo_9x4Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x5 takes 9 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_9x5(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x5_64 takes 9 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_9x5_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x5 takes 9 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_9x5(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x5_64Xor takes 9 inputs and produces 5 outputs.
//
//go:noescape
func mulGFNI_9x5_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x5Xor takes 9 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxGFNI_9x5Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x5Xor takes 9 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxTwo_9x5Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x6 takes 9 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_9x6(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x6_64 takes 9 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_9x6_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x6 takes 9 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_9x6(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x6_64Xor takes 9 inputs and produces 6 outputs.
//
//go:noescape
func mulGFNI_9x6_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x6Xor takes 9 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxGFNI_9x6Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x6Xor takes 9 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxTwo_9x6Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x7 takes 9 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_9x7(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x7_64 takes 9 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_9x7_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x7 takes 9 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_9x7(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x7_64Xor takes 9 inputs and produces 7 outputs.
//
//go:noescape
func mulGFNI_9x7_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x7Xor takes 9 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxGFNI_9x7Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x7Xor takes 9 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxTwo_9x7Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x8 takes 9 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_9x8(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x8_64 takes 9 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_9x8_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x8 takes 9 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_9x8(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x8_64Xor takes 9 inputs and produces 8 outputs.
//
//go:noescape
func mulGFNI_9x8_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x8Xor takes 9 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxGFNI_9x8Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x8Xor takes 9 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxTwo_9x8Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x9 takes 9 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_9x9(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x9_64 takes 9 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_9x9_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x9 takes 9 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_9x9(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x9_64Xor takes 9 inputs and produces 9 outputs.
//
//go:noescape
func mulGFNI_9x9_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x9Xor takes 9 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxGFNI_9x9Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x9Xor takes 9 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxTwo_9x9Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x10 takes 9 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_9x10(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x10_64 takes 9 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_9x10_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x10 takes 9 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_9x10(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_9x10_64Xor takes 9 inputs and produces 10 outputs.
//
//go:noescape
func mulGFNI_9x10_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_9x10Xor takes 9 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxGFNI_9x10Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_9x10Xor takes 9 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxTwo_9x10Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x1_64 takes 10 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_10x1_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x1_64 takes 10 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_10x1_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x1 takes 10 inputs and produces 1 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_10x1(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x1_64Xor takes 10 inputs and produces 1 outputs.
//
//go:noescape
func mulGFNI_10x1_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x1Xor takes 10 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxGFNI_10x1Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x1_64Xor takes 10 inputs and produces 1 outputs.
//
//go:noescape
func mulAvxTwo_10x1_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x2_64 takes 10 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_10x2_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x2_64 takes 10 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_10x2_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x2 takes 10 inputs and produces 2 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_10x2(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x2_64Xor takes 10 inputs and produces 2 outputs.
//
//go:noescape
func mulGFNI_10x2_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x2Xor takes 10 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxGFNI_10x2Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x2_64Xor takes 10 inputs and produces 2 outputs.
//
//go:noescape
func mulAvxTwo_10x2_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x3_64 takes 10 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_10x3_64(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x3_64 takes 10 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_10x3_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x3 takes 10 inputs and produces 3 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_10x3(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x3_64Xor takes 10 inputs and produces 3 outputs.
//
//go:noescape
func mulGFNI_10x3_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x3Xor takes 10 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxGFNI_10x3Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x3_64Xor takes 10 inputs and produces 3 outputs.
//
//go:noescape
func mulAvxTwo_10x3_64Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x4 takes 10 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_10x4(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x4_64 takes 10 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_10x4_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x4 takes 10 inputs and produces 4 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_10x4(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x4_64Xor takes 10 inputs and produces 4 outputs.
//
//go:noescape
func mulGFNI_10x4_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x4Xor takes 10 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxGFNI_10x4Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x4Xor takes 10 inputs and produces 4 outputs.
//
//go:noescape
func mulAvxTwo_10x4Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x5 takes 10 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_10x5(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x5_64 takes 10 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_10x5_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x5 takes 10 inputs and produces 5 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_10x5(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x5_64Xor takes 10 inputs and produces 5 outputs.
//
//go:noescape
func mulGFNI_10x5_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x5Xor takes 10 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxGFNI_10x5Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x5Xor takes 10 inputs and produces 5 outputs.
//
//go:noescape
func mulAvxTwo_10x5Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x6 takes 10 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_10x6(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x6_64 takes 10 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_10x6_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x6 takes 10 inputs and produces 6 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_10x6(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x6_64Xor takes 10 inputs and produces 6 outputs.
//
//go:noescape
func mulGFNI_10x6_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x6Xor takes 10 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxGFNI_10x6Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x6Xor takes 10 inputs and produces 6 outputs.
//
//go:noescape
func mulAvxTwo_10x6Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x7 takes 10 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_10x7(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x7_64 takes 10 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_10x7_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x7 takes 10 inputs and produces 7 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_10x7(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x7_64Xor takes 10 inputs and produces 7 outputs.
//
//go:noescape
func mulGFNI_10x7_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x7Xor takes 10 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxGFNI_10x7Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x7Xor takes 10 inputs and produces 7 outputs.
//
//go:noescape
func mulAvxTwo_10x7Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x8 takes 10 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_10x8(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x8_64 takes 10 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_10x8_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x8 takes 10 inputs and produces 8 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_10x8(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x8_64Xor takes 10 inputs and produces 8 outputs.
//
//go:noescape
func mulGFNI_10x8_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x8Xor takes 10 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxGFNI_10x8Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x8Xor takes 10 inputs and produces 8 outputs.
//
//go:noescape
func mulAvxTwo_10x8Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x9 takes 10 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_10x9(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x9_64 takes 10 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_10x9_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x9 takes 10 inputs and produces 9 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_10x9(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x9_64Xor takes 10 inputs and produces 9 outputs.
//
//go:noescape
func mulGFNI_10x9_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x9Xor takes 10 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxGFNI_10x9Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x9Xor takes 10 inputs and produces 9 outputs.
//
//go:noescape
func mulAvxTwo_10x9Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x10 takes 10 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxTwo_10x10(matrix []byte, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x10_64 takes 10 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulGFNI_10x10_64(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x10 takes 10 inputs and produces 10 outputs.
// The output is initialized to 0.
//
//go:noescape
func mulAvxGFNI_10x10(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulGFNI_10x10_64Xor takes 10 inputs and produces 10 outputs.
//
//go:noescape
func mulGFNI_10x10_64Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxGFNI_10x10Xor takes 10 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxGFNI_10x10Xor(matrix []uint64, in [][]byte, out [][]byte, start int, n int)

// mulAvxTwo_10x10Xor takes 10 inputs and produces 10 outputs.
//
//go:noescape
func mulAvxTwo_10x10Xor(matrix []byte, in [][]byte, out [][]byte, start int, n int)

//go:noescape
func ifftDIT2_avx2(x []byte, y []byte, table *[128]uint8)

//go:noescape
func fftDIT2_avx2(x []byte, y []byte, table *[128]uint8)

//go:noescape
func mulgf16_avx2(x []byte, y []byte, table *[128]uint8)

//go:noescape
func ifftDIT4_avx512_0(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx512_0(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx512_1(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx512_1(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx512_2(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx512_2(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx512_3(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx512_3(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx512_4(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx512_4(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx512_5(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx512_5(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx512_6(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx512_6(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx512_7(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx512_7(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx2_0(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx2_0(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx2_1(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx2_1(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx2_2(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx2_2(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx2_3(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx2_3(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx2_4(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx2_4(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx2_5(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx2_5(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx2_6(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx2_6(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT4_avx2_7(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func fftDIT4_avx2_7(work [][]byte, dist int, table01 *[128]uint8, table23 *[128]uint8, table02 *[128]uint8)

//go:noescape
func ifftDIT2_ssse3(x []byte, y []byte, table *[128]uint8)

//go:noescape
func fftDIT2_ssse3(x []byte, y []byte, table *[128]uint8)

//go:noescape
func mulgf16_ssse3(x []byte, y []byte, table *[128]uint8)

//go:noescape
func ifftDIT28_avx2(x []byte, y []byte, table *[32]uint8)

//go:noescape
func fftDIT28_avx2(x []byte, y []byte, table *[32]uint8)

//go:noescape
func ifftDIT48_avx2_0(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func fftDIT48_avx2_0(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func ifftDIT48_avx2_1(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func fftDIT48_avx2_1(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func ifftDIT48_avx2_2(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func fftDIT48_avx2_2(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func ifftDIT48_avx2_3(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func fftDIT48_avx2_3(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func ifftDIT48_avx2_4(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func fftDIT48_avx2_4(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func ifftDIT48_avx2_5(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func fftDIT48_avx2_5(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func ifftDIT48_avx2_6(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func fftDIT48_avx2_6(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func ifftDIT48_avx2_7(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func fftDIT48_avx2_7(work [][]byte, dist int, t01 *[32]uint8, t23 *[32]uint8, t02 *[32]uint8)

//go:noescape
func ifftDIT48_gfni_0(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func fftDIT48_gfni_0(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func ifftDIT48_gfni_1(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func fftDIT48_gfni_1(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func ifftDIT48_gfni_2(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func fftDIT48_gfni_2(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func ifftDIT48_gfni_3(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func fftDIT48_gfni_3(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func ifftDIT48_gfni_4(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func fftDIT48_gfni_4(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func ifftDIT48_gfni_5(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func fftDIT48_gfni_5(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func ifftDIT48_gfni_6(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func fftDIT48_gfni_6(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func ifftDIT48_gfni_7(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)

//go:noescape
func fftDIT48_gfni_7(work [][]byte, dist int, t01 uint64, t23 uint64, t02 uint64)
