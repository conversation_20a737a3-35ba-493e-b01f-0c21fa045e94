// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.26.1
// source: cnameManager.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CnameMap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Config map[string]string `protobuf:"bytes,1,rep,name=config,proto3" json:"config,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CnameMap) Reset() {
	*x = CnameMap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_cnameManager_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CnameMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CnameMap) ProtoMessage() {}

func (x *CnameMap) ProtoReflect() protoreflect.Message {
	mi := &file_cnameManager_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CnameMap.ProtoReflect.Descriptor instead.
func (*CnameMap) Descriptor() ([]byte, []int) {
	return file_cnameManager_proto_rawDescGZIP(), []int{0}
}

func (x *CnameMap) GetConfig() map[string]string {
	if x != nil {
		return x.Config
	}
	return nil
}

var File_cnameManager_proto protoreflect.FileDescriptor

var file_cnameManager_proto_rawDesc = []byte{
	0x0a, 0x12, 0x63, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x70, 0x62, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x77, 0x0a, 0x08, 0x43, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x70, 0x12,
	0x30, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x70, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x1a, 0x39, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xca, 0x02, 0x0a,
	0x0c, 0x43, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x4d, 0x0a,
	0x0d, 0x47, 0x65, 0x74, 0x43, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x1c,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x1a, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x00, 0x12, 0x35, 0x0a, 0x0b,
	0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x43, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x61,
	0x70, 0x22, 0x00, 0x12, 0x36, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x43, 0x6e, 0x61, 0x6d, 0x65, 0x42,
	0x79, 0x4b, 0x65, 0x79, 0x12, 0x0c, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x6e, 0x61, 0x6d, 0x65, 0x4d,
	0x61, 0x70, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x34, 0x0a, 0x0b, 0x44,
	0x65, 0x6c, 0x41, 0x6c, 0x6c, 0x43, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0c, 0x2e, 0x70, 0x62, 0x2e,
	0x43, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x70, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x46, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x43, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x79, 0x4b,
	0x65, 0x79, 0x12, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x3b, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_cnameManager_proto_rawDescOnce sync.Once
	file_cnameManager_proto_rawDescData = file_cnameManager_proto_rawDesc
)

func file_cnameManager_proto_rawDescGZIP() []byte {
	file_cnameManager_proto_rawDescOnce.Do(func() {
		file_cnameManager_proto_rawDescData = protoimpl.X.CompressGZIP(file_cnameManager_proto_rawDescData)
	})
	return file_cnameManager_proto_rawDescData
}

var file_cnameManager_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_cnameManager_proto_goTypes = []interface{}{
	(*CnameMap)(nil),               // 0: pb.CnameMap
	nil,                            // 1: pb.CnameMap.ConfigEntry
	(*wrapperspb.StringValue)(nil), // 2: google.protobuf.StringValue
	(*emptypb.Empty)(nil),          // 3: google.protobuf.Empty
	(*OperationResponse)(nil),      // 4: pb.OperationResponse
}
var file_cnameManager_proto_depIdxs = []int32{
	1, // 0: pb.CnameMap.config:type_name -> pb.CnameMap.ConfigEntry
	2, // 1: pb.CnameManager.GetCnameByKey:input_type -> google.protobuf.StringValue
	3, // 2: pb.CnameManager.GetAllCname:input_type -> google.protobuf.Empty
	0, // 3: pb.CnameManager.SetCnameByKey:input_type -> pb.CnameMap
	0, // 4: pb.CnameManager.DelAllCname:input_type -> pb.CnameMap
	2, // 5: pb.CnameManager.DelCnameByKey:input_type -> google.protobuf.StringValue
	2, // 6: pb.CnameManager.GetCnameByKey:output_type -> google.protobuf.StringValue
	0, // 7: pb.CnameManager.GetAllCname:output_type -> pb.CnameMap
	4, // 8: pb.CnameManager.SetCnameByKey:output_type -> pb.OperationResponse
	4, // 9: pb.CnameManager.DelAllCname:output_type -> pb.OperationResponse
	4, // 10: pb.CnameManager.DelCnameByKey:output_type -> pb.OperationResponse
	6, // [6:11] is the sub-list for method output_type
	1, // [1:6] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_cnameManager_proto_init() }
func file_cnameManager_proto_init() {
	if File_cnameManager_proto != nil {
		return
	}
	file_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_cnameManager_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CnameMap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_cnameManager_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_cnameManager_proto_goTypes,
		DependencyIndexes: file_cnameManager_proto_depIdxs,
		MessageInfos:      file_cnameManager_proto_msgTypes,
	}.Build()
	File_cnameManager_proto = out.File
	file_cnameManager_proto_rawDesc = nil
	file_cnameManager_proto_goTypes = nil
	file_cnameManager_proto_depIdxs = nil
}
