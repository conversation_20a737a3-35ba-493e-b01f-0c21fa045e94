os:
  - linux

language: go

go:
  - 1.14.x

env:
  global:
    - BUILD_DEPTYPE=gomod
  matrix:
    - GOTFLAGS="-race"
    - GOTFLAGS="-count 5"


# disable travis install
install:
  - true

script:
  - bash <(curl -s https://raw.githubusercontent.com/ipfs/ci-helpers/master/travis-ci/run-standard-tests.sh)


cache:
  directories:
    - $GOPATH/pkg/mod
    - $HOME/.cache/go-build

notifications:
  email: false
