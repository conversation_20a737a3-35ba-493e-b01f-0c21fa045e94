package manager

import (
	"encoding/json"
	"log"
	"time"
)

// TODO 根据配置文件确定从内存获取映射表还是redis
func (sm *SessionsManager) GetOneHttpProxy(domain string) (*HttpProxy, error) {
	log.Println("query:", domain)
	hpBytes, err := sm.HttpProxyRuntimeStorage.GetValueByKeyToBytes(domain)
	if err != nil {
		return nil, err
	}
	var httpProxyModel = &HttpProxy{}
	err = json.Unmarshal(hpBytes, httpProxyModel)
	if err != nil {
		return nil, err
	}
	//TODO Update Status
	return httpProxyModel, nil
}

func (sm *SessionsManager) GetAllHttpProxy() map[string]*HttpProxy {
	var HttpProxyMap = make(map[string]*HttpProxy)
	keys, err := sm.HttpProxyRuntimeStorage.GetAllKeys()
	if err != nil {
		return HttpProxyMap
	}
	for _, key := range keys {
		hpBytes, err := sm.HttpProxyRuntimeStorage.GetValueByKeyToBytes(key)
		if err != nil {
			continue
		}
		var httpProxyModel = &HttpProxy{}
		err = json.Unmarshal(hpBytes, httpProxyModel)
		if err != nil {
			continue
		}
		HttpProxyMap[key] = httpProxyModel
	}
	return HttpProxyMap
}

func (sm *SessionsManager) AddOrUpdateHttpProxy(httpProxy *HttpProxy) error {
	httpProxyBytes, err := json.Marshal(httpProxy)
	if err != nil {
		return err
	}
	return sm.HttpProxyRuntimeStorage.SetValueByKey(httpProxy.Domain, httpProxyBytes)
}

func (sm *SessionsManager) DelHttpProxy(domain string) {
	sm.HttpProxyRuntimeStorage.DelValueByKey(domain)
}

func (sm *SessionsManager) UpdateHttpProxyByMap(HttpProxyMap map[string]*HttpProxy) {
	for _, hp := range HttpProxyMap {
		sm.AddOrUpdateHttpProxy(hp)
	}
}

// 启动HTTP穿透自动清理任务
// timeout: 超时时间（分钟）
func (sm *SessionsManager) StartHttpProxyCleanupTask(timeout int) {
	if timeout <= 0 {
		timeout = 60 * 24 * 7 // 默认7天
	}

	go func() {
		ticker := time.NewTicker(10 * time.Minute) // 每10分钟检查一次
		defer ticker.Stop()

		for range ticker.C {
			sm.CleanupInactiveHttpProxies(timeout)
		}
	}()

	log.Printf("HTTP穿透自动清理任务已启动，超时时间: %d分钟", timeout)
}

// 清理长时间未使用的HTTP穿透
func (sm *SessionsManager) CleanupInactiveHttpProxies(timeoutMinutes int) {
	proxies := sm.GetAllHttpProxy()
	timeout := time.Duration(timeoutMinutes) * time.Minute
	now := time.Now()
	cleaned := 0

	for domain, proxy := range proxies {
		// 如果LastActive是零值（从未被访问过），则使用当前时间减去超时时间的一半作为初始值
		lastActive := proxy.LastActive
		if lastActive.IsZero() {
			lastActive = now.Add(-timeout / 2)
		}

		// 如果超过超时时间未活跃，则删除
		if now.Sub(lastActive) > timeout {
			sm.DelHttpProxy(domain)
			log.Printf("已清理长时间未使用的HTTP穿透: %s, 最后活跃: %v", domain, lastActive.Format("2006-01-02 15:04:05"))
			cleaned++
		}
	}

	if cleaned > 0 {
		log.Printf("HTTP穿透自动清理完成，共清理 %d 个穿透", cleaned)
	}
}
