# This is an example goreleaser.yaml file with some sane defaults.
# Make sure to check the documentation at http://goreleaser.com

builds:
  -
    id: "cpuid"
    binary: cpuid
    main: ./cmd/cpuid/main.go
    env:
      - CGO_ENABLED=0
    flags:
      - -ldflags=-s -w
    goos:
      - aix
      - linux
      - freebsd
      - netbsd
      - windows
      - darwin
    goarch:
      - 386
      - amd64
      - arm64
    goarm:
      - 7

archives:
  -
    id: cpuid
    name_template: "cpuid-{{ .Os }}_{{ .Arch }}_{{ .Version }}"
    replacements:
      aix: AIX
      darwin: OSX
      linux: Linux
      windows: Windows
      386: i386
      amd64: x86_64
      freebsd: FreeBSD
      netbsd: NetBSD
    format_overrides:
      - goos: windows
        format: zip
    files:
      - LICENSE
checksum:
  name_template: 'checksums.txt'
snapshot:
  name_template: "{{ .Tag }}-next"
changelog:
  sort: asc
  filters:
    exclude:
    - '^doc:'
    - '^docs:'
    - '^test:'
    - '^tests:'
    - '^Update\sREADME.md'

nfpms:
  -
    file_name_template: "cpuid_package_{{ .Version }}_{{ .Os }}_{{ .Arch }}"
    vendor: <PERSON>
    homepage: https://github.com/klauspost/cpuid
    maintainer: Klaus Post <<EMAIL>>
    description: CPUID Tool
    license: BSD 3-Clause
    formats:
      - deb
      - rpm
    replacements:
      darwin: Darwin
      linux: Linux
      freebsd: FreeBSD
      amd64: x86_64
