// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v5.26.1
// source: cnameManager.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// CnameManagerClient is the client API for CnameManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CnameManagerClient interface {
	// 用户别名
	// 普通配置一次性操作多个
	GetCnameByKey(ctx context.Context, in *wrapperspb.StringValue, opts ...grpc.CallOption) (*wrapperspb.StringValue, error)
	GetAllCname(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*CnameMap, error)
	// 创建或者更新
	SetCnameByKey(ctx context.Context, in *CnameMap, opts ...grpc.CallOption) (*OperationResponse, error)
	// 删除
	DelAllCname(ctx context.Context, in *CnameMap, opts ...grpc.CallOption) (*OperationResponse, error)
	DelCnameByKey(ctx context.Context, in *wrapperspb.StringValue, opts ...grpc.CallOption) (*OperationResponse, error)
}

type cnameManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewCnameManagerClient(cc grpc.ClientConnInterface) CnameManagerClient {
	return &cnameManagerClient{cc}
}

func (c *cnameManagerClient) GetCnameByKey(ctx context.Context, in *wrapperspb.StringValue, opts ...grpc.CallOption) (*wrapperspb.StringValue, error) {
	out := new(wrapperspb.StringValue)
	err := c.cc.Invoke(ctx, "/pb.CnameManager/GetCnameByKey", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cnameManagerClient) GetAllCname(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*CnameMap, error) {
	out := new(CnameMap)
	err := c.cc.Invoke(ctx, "/pb.CnameManager/GetAllCname", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cnameManagerClient) SetCnameByKey(ctx context.Context, in *CnameMap, opts ...grpc.CallOption) (*OperationResponse, error) {
	out := new(OperationResponse)
	err := c.cc.Invoke(ctx, "/pb.CnameManager/SetCnameByKey", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cnameManagerClient) DelAllCname(ctx context.Context, in *CnameMap, opts ...grpc.CallOption) (*OperationResponse, error) {
	out := new(OperationResponse)
	err := c.cc.Invoke(ctx, "/pb.CnameManager/DelAllCname", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cnameManagerClient) DelCnameByKey(ctx context.Context, in *wrapperspb.StringValue, opts ...grpc.CallOption) (*OperationResponse, error) {
	out := new(OperationResponse)
	err := c.cc.Invoke(ctx, "/pb.CnameManager/DelCnameByKey", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CnameManagerServer is the server API for CnameManager service.
// All implementations must embed UnimplementedCnameManagerServer
// for forward compatibility
type CnameManagerServer interface {
	// 用户别名
	// 普通配置一次性操作多个
	GetCnameByKey(context.Context, *wrapperspb.StringValue) (*wrapperspb.StringValue, error)
	GetAllCname(context.Context, *emptypb.Empty) (*CnameMap, error)
	// 创建或者更新
	SetCnameByKey(context.Context, *CnameMap) (*OperationResponse, error)
	// 删除
	DelAllCname(context.Context, *CnameMap) (*OperationResponse, error)
	DelCnameByKey(context.Context, *wrapperspb.StringValue) (*OperationResponse, error)
	mustEmbedUnimplementedCnameManagerServer()
}

// UnimplementedCnameManagerServer must be embedded to have forward compatible implementations.
type UnimplementedCnameManagerServer struct {
}

func (UnimplementedCnameManagerServer) GetCnameByKey(context.Context, *wrapperspb.StringValue) (*wrapperspb.StringValue, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCnameByKey not implemented")
}
func (UnimplementedCnameManagerServer) GetAllCname(context.Context, *emptypb.Empty) (*CnameMap, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllCname not implemented")
}
func (UnimplementedCnameManagerServer) SetCnameByKey(context.Context, *CnameMap) (*OperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCnameByKey not implemented")
}
func (UnimplementedCnameManagerServer) DelAllCname(context.Context, *CnameMap) (*OperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelAllCname not implemented")
}
func (UnimplementedCnameManagerServer) DelCnameByKey(context.Context, *wrapperspb.StringValue) (*OperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelCnameByKey not implemented")
}
func (UnimplementedCnameManagerServer) mustEmbedUnimplementedCnameManagerServer() {}

// UnsafeCnameManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CnameManagerServer will
// result in compilation errors.
type UnsafeCnameManagerServer interface {
	mustEmbedUnimplementedCnameManagerServer()
}

func RegisterCnameManagerServer(s grpc.ServiceRegistrar, srv CnameManagerServer) {
	s.RegisterService(&CnameManager_ServiceDesc, srv)
}

func _CnameManager_GetCnameByKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wrapperspb.StringValue)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CnameManagerServer).GetCnameByKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.CnameManager/GetCnameByKey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CnameManagerServer).GetCnameByKey(ctx, req.(*wrapperspb.StringValue))
	}
	return interceptor(ctx, in, info, handler)
}

func _CnameManager_GetAllCname_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CnameManagerServer).GetAllCname(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.CnameManager/GetAllCname",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CnameManagerServer).GetAllCname(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _CnameManager_SetCnameByKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CnameMap)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CnameManagerServer).SetCnameByKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.CnameManager/SetCnameByKey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CnameManagerServer).SetCnameByKey(ctx, req.(*CnameMap))
	}
	return interceptor(ctx, in, info, handler)
}

func _CnameManager_DelAllCname_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CnameMap)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CnameManagerServer).DelAllCname(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.CnameManager/DelAllCname",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CnameManagerServer).DelAllCname(ctx, req.(*CnameMap))
	}
	return interceptor(ctx, in, info, handler)
}

func _CnameManager_DelCnameByKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(wrapperspb.StringValue)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CnameManagerServer).DelCnameByKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.CnameManager/DelCnameByKey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CnameManagerServer).DelCnameByKey(ctx, req.(*wrapperspb.StringValue))
	}
	return interceptor(ctx, in, info, handler)
}

// CnameManager_ServiceDesc is the grpc.ServiceDesc for CnameManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CnameManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.CnameManager",
	HandlerType: (*CnameManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCnameByKey",
			Handler:    _CnameManager_GetCnameByKey_Handler,
		},
		{
			MethodName: "GetAllCname",
			Handler:    _CnameManager_GetAllCname_Handler,
		},
		{
			MethodName: "SetCnameByKey",
			Handler:    _CnameManager_SetCnameByKey_Handler,
		},
		{
			MethodName: "DelAllCname",
			Handler:    _CnameManager_DelAllCname_Handler,
		},
		{
			MethodName: "DelCnameByKey",
			Handler:    _CnameManager_DelCnameByKey_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cnameManager.proto",
}
