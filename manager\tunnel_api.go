package manager

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
)

// TunnelAPI 穿透服务的API处理器
type TunnelAPI struct {
	tunnelManager  *TunnelManager
	authMiddleware *AuthMiddleware
}

// 创建穿透API处理器
func NewTunnelAPI(tm *TunnelManager, apiKey string, authEnabled bool) *TunnelAPI {
	return &TunnelAPI{
		tunnelManager:  tm,
		authMiddleware: NewAuthMiddleware(apiKey, authEnabled),
	}
}

// API响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// 发送JSON响应
func (api *TunnelAPI) sendJSONResponse(w http.ResponseWriter, response APIResponse) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

// 添加穿透服务 POST /api/tunnel
func (api *TunnelAPI) AddTunnel(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "只支持POST方法",
		})
		return
	}

	var config TunnelConfig
	err := json.NewDecoder(r.Body).Decode(&config)
	if err != nil {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: fmt.Sprintf("解析请求失败: %s", err.Error()),
		})
		return
	}

	// 验证必要参数
	if config.TunnelType == "" || config.ListenPort == 0 || config.RunId == "" {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "缺少必要参数: tunnelType, listenPort, runId",
		})
		return
	}

	// 添加穿透服务
	err = api.tunnelManager.AddTunnel(&config)
	if err != nil {
		api.sendJSONResponse(w, APIResponse{
			Code:    500,
			Message: fmt.Sprintf("添加穿透服务失败: %s", err.Error()),
		})
		return
	}

	api.sendJSONResponse(w, APIResponse{
		Code:    0,
		Message: "穿透服务添加成功",
		Data:    config,
	})
}

// 删除穿透服务 DELETE /api/tunnel/{tunnelID}
func (api *TunnelAPI) RemoveTunnel(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "只支持DELETE方法",
		})
		return
	}

	// 从URL路径中提取tunnelID
	parts := strings.Split(strings.Trim(r.URL.Path, "/"), "/")
	if len(parts) < 3 {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "URL格式错误，应为 /api/tunnel/{tunnelID}",
		})
		return
	}

	tunnelID := parts[2]
	err := api.tunnelManager.RemoveTunnel(tunnelID)
	if err != nil {
		api.sendJSONResponse(w, APIResponse{
			Code:    500,
			Message: fmt.Sprintf("删除穿透服务失败: %s", err.Error()),
		})
		return
	}

	api.sendJSONResponse(w, APIResponse{
		Code:    0,
		Message: "穿透服务删除成功",
	})
}

// 获取所有穿透服务 GET /api/tunnel
func (api *TunnelAPI) GetAllTunnels(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "只支持GET方法",
		})
		return
	}

	tunnels := api.tunnelManager.GetAllTunnels()
	api.sendJSONResponse(w, APIResponse{
		Code:    0,
		Message: "获取成功",
		Data:    tunnels,
	})
}

// 获取单个穿透服务 GET /api/tunnel/{tunnelID}
func (api *TunnelAPI) GetTunnel(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "只支持GET方法",
		})
		return
	}

	// 从URL路径中提取tunnelID
	parts := strings.Split(strings.Trim(r.URL.Path, "/"), "/")
	if len(parts) < 3 {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "URL格式错误，应为 /api/tunnel/{tunnelID}",
		})
		return
	}

	tunnelID := parts[2]
	config, err := api.tunnelManager.GetTunnel(tunnelID)
	if err != nil {
		api.sendJSONResponse(w, APIResponse{
			Code:    404,
			Message: fmt.Sprintf("穿透服务不存在: %s", err.Error()),
		})
		return
	}

	api.sendJSONResponse(w, APIResponse{
		Code:    0,
		Message: "获取成功",
		Data:    config,
	})
}

// 重启穿透服务 POST /api/tunnel/{tunnelID}/restart
func (api *TunnelAPI) RestartTunnel(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "只支持POST方法",
		})
		return
	}

	// 从URL路径中提取tunnelID
	parts := strings.Split(strings.Trim(r.URL.Path, "/"), "/")
	if len(parts) < 4 || parts[3] != "restart" {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "URL格式错误，应为 /api/tunnel/{tunnelID}/restart",
		})
		return
	}

	tunnelID := parts[2]
	err := api.tunnelManager.RestartTunnel(tunnelID)
	if err != nil {
		api.sendJSONResponse(w, APIResponse{
			Code:    500,
			Message: fmt.Sprintf("重启穿透服务失败: %s", err.Error()),
		})
		return
	}

	api.sendJSONResponse(w, APIResponse{
		Code:    0,
		Message: "穿透服务重启成功",
	})
}

// 获取穿透服务统计信息 GET /api/tunnel/stats
func (api *TunnelAPI) GetTunnelStats(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "只支持GET方法",
		})
		return
	}

	stats := api.tunnelManager.GetTunnelStats()
	api.sendJSONResponse(w, APIResponse{
		Code:    0,
		Message: "获取统计信息成功",
		Data:    stats,
	})
}

// 添加HTTP穿透配置 POST /api/http
func (api *TunnelAPI) AddHTTPProxy(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "只支持POST方法",
		})
		return
	}

	var config struct {
		Domain      string `json:"domain"`
		RunId       string `json:"runId"`
		RemoteIP    string `json:"remoteIP"`
		RemotePort  int    `json:"remotePort"`
		UserName    string `json:"userName,omitempty"`
		Password    string `json:"password,omitempty"`
		IfHttps     bool   `json:"ifHttps,omitempty"`
		Description string `json:"description,omitempty"`
	}

	err := json.NewDecoder(r.Body).Decode(&config)
	if err != nil {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: fmt.Sprintf("解析请求失败: %s", err.Error()),
		})
		return
	}

	// 验证必要参数
	if config.Domain == "" || config.RunId == "" || config.RemoteIP == "" || config.RemotePort == 0 {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "缺少必要参数: domain, runId, remoteIP, remotePort",
		})
		return
	}

	// 创建HTTP穿透
	httpProxy := &HttpProxy{
		Domain:      config.Domain,
		RunId:       config.RunId,
		RemoteIP:    config.RemoteIP,
		RemotePort:  config.RemotePort,
		UserName:    config.UserName,
		Password:    config.Password,
		IfHttps:     config.IfHttps,
		Description: config.Description,
	}

	err = api.tunnelManager.sessionManager.AddOrUpdateHttpProxy(httpProxy)
	if err != nil {
		api.sendJSONResponse(w, APIResponse{
			Code:    500,
			Message: fmt.Sprintf("添加HTTP穿透失败: %s", err.Error()),
		})
		return
	}

	api.sendJSONResponse(w, APIResponse{
		Code:    0,
		Message: "HTTP穿透添加成功",
		Data:    httpProxy,
	})
}

// 删除HTTP穿透配置 DELETE /api/http/{domain}
func (api *TunnelAPI) RemoveHTTPProxy(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "只支持DELETE方法",
		})
		return
	}

	// 从URL路径中提取domain
	parts := strings.Split(strings.Trim(r.URL.Path, "/"), "/")
	if len(parts) < 3 {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "URL格式错误，应为 /api/http/{domain}",
		})
		return
	}

	domain := parts[2]
	api.tunnelManager.sessionManager.DelHttpProxy(domain)

	api.sendJSONResponse(w, APIResponse{
		Code:    0,
		Message: "HTTP穿透删除成功",
	})
}

// 获取所有HTTP穿透配置 GET /api/http
func (api *TunnelAPI) GetAllHTTPProxies(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "只支持GET方法",
		})
		return
	}

	proxies := api.tunnelManager.sessionManager.GetAllHttpProxy()
	api.sendJSONResponse(w, APIResponse{
		Code:    0,
		Message: "获取成功",
		Data:    proxies,
	})
}

// 获取单个HTTP穿透配置 GET /api/http/{domain}
func (api *TunnelAPI) GetHTTPProxy(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "只支持GET方法",
		})
		return
	}

	// 从URL路径中提取domain
	parts := strings.Split(strings.Trim(r.URL.Path, "/"), "/")
	if len(parts) < 3 {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "URL格式错误，应为 /api/http/{domain}",
		})
		return
	}

	domain := parts[2]
	proxy, err := api.tunnelManager.sessionManager.GetOneHttpProxy(domain)
	if err != nil {
		api.sendJSONResponse(w, APIResponse{
			Code:    404,
			Message: fmt.Sprintf("HTTP穿透不存在: %s", err.Error()),
		})
		return
	}

	api.sendJSONResponse(w, APIResponse{
		Code:    0,
		Message: "获取成功",
		Data:    proxy,
	})
}

// 设置HTTP穿透超时时间 POST /api/http/timeout
func (api *TunnelAPI) SetHTTPProxyTimeout(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "只支持POST方法",
		})
		return
	}

	var config struct {
		TimeoutMinutes int `json:"timeoutMinutes"`
	}

	err := json.NewDecoder(r.Body).Decode(&config)
	if err != nil {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: fmt.Sprintf("解析请求失败: %s", err.Error()),
		})
		return
	}

	// 验证超时时间
	if config.TimeoutMinutes <= 0 {
		api.sendJSONResponse(w, APIResponse{
			Code:    400,
			Message: "超时时间必须大于0分钟",
		})
		return
	}

	// 立即执行一次清理，并重新设置定时任务
	go func() {
		api.tunnelManager.sessionManager.CleanupInactiveHttpProxies(config.TimeoutMinutes)
		api.tunnelManager.sessionManager.StartHttpProxyCleanupTask(config.TimeoutMinutes)
	}()

	api.sendJSONResponse(w, APIResponse{
		Code:    0,
		Message: fmt.Sprintf("HTTP穿透超时时间已设置为 %d 分钟", config.TimeoutMinutes),
		Data:    config.TimeoutMinutes,
	})
}

// 启动API服务器
func (api *TunnelAPI) StartAPIServer(port int) error {
	// 设置路由
	http.HandleFunc("/api/tunnel/stats", api.authMiddleware.Middleware(api.GetTunnelStats))
	http.HandleFunc("/api/tunnel", api.authMiddleware.Middleware(func(w http.ResponseWriter, r *http.Request) {
		switch r.Method {
		case http.MethodGet:
			api.GetAllTunnels(w, r)
		case http.MethodPost:
			api.AddTunnel(w, r)
		default:
			api.sendJSONResponse(w, APIResponse{
				Code:    405,
				Message: "不支持的HTTP方法",
			})
		}
	}))

	// 处理单个穿透服务的操作
	http.HandleFunc("/api/tunnel/", api.authMiddleware.Middleware(func(w http.ResponseWriter, r *http.Request) {
		path := strings.Trim(r.URL.Path, "/")
		parts := strings.Split(path, "/")

		if len(parts) < 3 {
			api.sendJSONResponse(w, APIResponse{
				Code:    400,
				Message: "URL格式错误",
			})
			return
		}

		if len(parts) == 3 {
			// /api/tunnel/{tunnelID}
			switch r.Method {
			case http.MethodGet:
				api.GetTunnel(w, r)
			case http.MethodDelete:
				api.RemoveTunnel(w, r)
			default:
				api.sendJSONResponse(w, APIResponse{
					Code:    405,
					Message: "不支持的HTTP方法",
				})
			}
		} else if len(parts) == 4 && parts[3] == "restart" {
			// /api/tunnel/{tunnelID}/restart
			api.RestartTunnel(w, r)
		} else {
			api.sendJSONResponse(w, APIResponse{
				Code:    404,
				Message: "API路径不存在",
			})
		}
	}))

	// 添加HTTP穿透API路由
	http.HandleFunc("/api/http", api.authMiddleware.Middleware(func(w http.ResponseWriter, r *http.Request) {
		switch r.Method {
		case http.MethodGet:
			api.GetAllHTTPProxies(w, r)
		case http.MethodPost:
			api.AddHTTPProxy(w, r)
		default:
			api.sendJSONResponse(w, APIResponse{
				Code:    405,
				Message: "不支持的HTTP方法",
			})
		}
	}))

	// 处理单个HTTP穿透的操作
	http.HandleFunc("/api/http/", api.authMiddleware.Middleware(func(w http.ResponseWriter, r *http.Request) {
		path := strings.Trim(r.URL.Path, "/")
		parts := strings.Split(path, "/")

		if len(parts) < 3 {
			api.sendJSONResponse(w, APIResponse{
				Code:    400,
				Message: "URL格式错误",
			})
			return
		}

		// /api/http/{domain}
		switch r.Method {
		case http.MethodGet:
			api.GetHTTPProxy(w, r)
		case http.MethodDelete:
			api.RemoveHTTPProxy(w, r)
		default:
			api.sendJSONResponse(w, APIResponse{
				Code:    405,
				Message: "不支持的HTTP方法",
			})
		}
	}))

	// 添加HTTP穿透超时设置API路由
	http.HandleFunc("/api/http/timeout", api.authMiddleware.Middleware(api.SetHTTPProxyTimeout))

	// 添加API密钥信息路由（不需要认证）
	http.HandleFunc("/api/auth/info", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			api.sendJSONResponse(w, APIResponse{
				Code:    405,
				Message: "只支持GET方法",
			})
			return
		}

		api.sendJSONResponse(w, APIResponse{
			Code:    0,
			Message: "获取API认证信息成功",
			Data: map[string]interface{}{
				"enabled": api.authMiddleware.Enabled,
			},
		})
	})

	// 启动HTTP服务器
	addr := fmt.Sprintf(":%d", port)
	fmt.Printf("穿透API服务器启动在端口: %d\n", port)
	return http.ListenAndServe(addr, nil)
}

// 便利方法：快速创建各种类型的穿透配置

// 创建TCP穿透配置
func CreateTCPTunnel(listenPort int, runId, remoteIP string, remotePort int, description string) *TunnelConfig {
	return &TunnelConfig{
		TunnelType:  TunnelTypeTCP,
		ListenPort:  listenPort,
		RunId:       runId,
		RemoteIP:    remoteIP,
		RemotePort:  remotePort,
		Description: description,
		ExtraParams: make(map[string]string),
	}
}

// 创建SSH穿透配置
// 注意：SSH穿透实际上使用TCP连接，但保留SSH类型便于管理和区分
// 用户名和密码参数不用于建立连接，但保留作为配置信息和未来扩展
func CreateSSHTunnel(listenPort int, runId, remoteIP string, remotePort int, userName, password, description string) *TunnelConfig {
	return &TunnelConfig{
		TunnelType:  TunnelTypeSSH,
		ListenPort:  listenPort,
		RunId:       runId,
		RemoteIP:    remoteIP,
		RemotePort:  remotePort,
		Description: description,
		ExtraParams: map[string]string{
			"userName": userName,
			"password": password,
		},
	}
}

// 创建WebSocket穿透配置
func CreateWebSocketTunnel(listenPort int, runId, remoteIP string, remotePort int, protocol, origin, targetUrl, description string) *TunnelConfig {
	extraParams := map[string]string{
		"protocol": protocol,
		"origin":   origin,
	}
	if targetUrl != "" {
		extraParams["targetUrl"] = targetUrl
	}

	return &TunnelConfig{
		TunnelType:  TunnelTypeWebSocket,
		ListenPort:  listenPort,
		RunId:       runId,
		RemoteIP:    remoteIP,
		RemotePort:  remotePort,
		Description: description,
		ExtraParams: extraParams,
	}
}

// 创建串口穿透配置
func CreateSerialPortTunnel(listenPort int, runId, portName, description string, baudRate, dataBits, stopBits, minimumReadSize int) *TunnelConfig {
	return &TunnelConfig{
		TunnelType:  TunnelTypeSerialPort,
		ListenPort:  listenPort,
		RunId:       runId,
		Description: description,
		ExtraParams: map[string]string{
			"portName":        portName,
			"baudRate":        strconv.Itoa(baudRate),
			"dataBits":        strconv.Itoa(dataBits),
			"stopBits":        strconv.Itoa(stopBits),
			"minimumReadSize": strconv.Itoa(minimumReadSize),
		},
	}
}
