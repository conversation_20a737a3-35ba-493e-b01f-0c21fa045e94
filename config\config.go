package config

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"

	"github.com/OpenIoTHub/server-go/utils"
	"github.com/OpenIoTHub/utils/models"
	"gopkg.in/yaml.v2"
)

var ConfigMode models.ServerConfig

func LoadConfig() (err error) {
	//是否是snapcraft应用，如果是则从snapcraft指定的工作目录保存配置文件
	appDataPath, havaAppDataPath := os.LookupEnv("SNAP_USER_DATA")
	if havaAppDataPath {
		DefaultConfigFilePath = filepath.Join(appDataPath, DefaultConfigFileName)
	}
	_, err = os.Stat(DefaultConfigFilePath)
	if err != nil {
		InitConfigFile()
	}
	log.Println("使用配置文件：", DefaultConfigFilePath)
	ConfigMode, ExtendedConfigMode, err = GetConfig(DefaultConfigFilePath)
	//解析日志配置
	writers := []io.Writer{}
	if ConfigMode.LogConfig != nil && ConfigMode.LogConfig.EnableStdout {
		writers = append(writers, os.Stdout)
	}
	if ConfigMode.LogConfig != nil && ConfigMode.LogConfig.LogFilePath != "" {
		var f *os.File
		f, err = os.OpenFile(ConfigMode.LogConfig.LogFilePath, os.O_WRONLY|os.O_CREATE|os.O_APPEND, 0644)
		if err != nil {
			log.Fatal(err)
		}
		writers = append(writers, f)
	}
	fileAndStdoutWriter := io.MultiWriter(writers...)
	log.SetOutput(fileAndStdoutWriter)
	//
	if err != nil {
		return
	}
	return
}

func InitConfigFile() {
	var err error
	ConfigMode.ServerUuid = utils.GetOneUUID()
	ConfigMode.LogConfig = &models.LogConfig{
		EnableStdout: true,
		LogFilePath:  "",
	}
	ConfigMode.Common.BindAddr = DefaultBindAddr
	ConfigMode.Common.KcpPort = DefaultKcpPort
	ConfigMode.Common.TcpPort = DefaultTcpPort
	ConfigMode.Common.TlsPort = DefaultTlsPort
	ConfigMode.Common.GrpcPort = DefaultGrpcPort
	ConfigMode.Common.HttpPort = DefaultHttpPort
	ConfigMode.Common.HttpsPort = DefaultHttpsPort
	ConfigMode.Common.UdpApiPort = DefaultUdpApiPort
	ConfigMode.Common.KcpApiPort = DefaultKcpApiPort
	ConfigMode.Security.LoginKey = DefaultLoginKey
	ConfigMode.RedisConfig.Network = DefaultRedisNetwork
	ConfigMode.RedisConfig.Address = DefaultRedisAddress

	// 初始化扩展配置
	ExtendedConfigMode.APIAuth.Enabled = DefaultAPIAuthEnabled
	ExtendedConfigMode.APIAuth.APIKey = utils.GenerateSecureToken(32)

	//	生成配置文件模板
	err = WriteConfigFile(ConfigMode, ExtendedConfigMode, DefaultConfigFilePath)
	if err == nil {
		fmt.Println("config created")
		return
	} else {
		log.Println("配置文件路径为：", DefaultConfigFilePath)
		log.Println("写入配置文件失败：", err.Error())
	}
}

// 从配置文件路径解析配置文件的内容
func GetConfig(configFilePath string) (configMode models.ServerConfig, extendedConfig ExtendedConfig, err error) {
	content, err := os.ReadFile(configFilePath)
	if err != nil {
		log.Println(err.Error())
		return
	}

	// 解析基本配置
	err = yaml.Unmarshal(content, &configMode)
	if err != nil {
		log.Println(err.Error())
		return
	}

	// 解析扩展配置
	err = yaml.Unmarshal(content, &extendedConfig)
	if err != nil {
		log.Println("解析扩展配置失败，使用默认值:", err.Error())
		extendedConfig = ExtendedConfig{
			APIAuth: APIAuthConfig{
				Enabled: DefaultAPIAuthEnabled,
				APIKey:  utils.GenerateSecureToken(32),
			},
		}
		err = nil // 重置错误，使用默认值继续
	}

	// 如果服务器UUID为空，生成新的
	if configMode.ServerUuid == "" {
		configMode.ServerUuid = utils.GetOneUUID()
		err = WriteConfigFile(configMode, extendedConfig, configFilePath)
		if err != nil {
			log.Println(err.Error())
			return
		}
	}

	// 如果API密钥为空，生成新的
	if extendedConfig.APIAuth.APIKey == "" {
		extendedConfig.APIAuth.APIKey = utils.GenerateSecureToken(32)
		err = WriteConfigFile(configMode, extendedConfig, configFilePath)
		if err != nil {
			log.Println(err.Error())
			return
		}
	}

	return
}

// WriteConfigFile 将配置内容写入指定的配置文件
func WriteConfigFile(configMode models.ServerConfig, extendedConfig ExtendedConfig, path string) (err error) {
	// 创建一个合并后的配置结构体
	type combinedConfig struct {
		models.ServerConfig `yaml:",inline"`
		APIAuth             APIAuthConfig `yaml:"api_auth"`
	}

	combined := combinedConfig{
		ServerConfig: configMode,
		APIAuth:      extendedConfig.APIAuth,
	}

	configByte, err := yaml.Marshal(combined)
	if err != nil {
		log.Println(err.Error())
		return
	}
	err = os.MkdirAll(filepath.Dir(path), 0644)
	if err != nil {
		return
	}
	err = os.WriteFile(path, configByte, 0644)
	return
}
