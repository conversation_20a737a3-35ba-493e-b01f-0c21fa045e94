/*
Package smetrics provides a bunch of algorithms for calculating
the distance between strings.

There are implementations for calculating the popular Levenshtein
distance (aka Edit Distance or Wagner-Fischer), as well as the Jaro
distance, the <PERSON><PERSON><PERSON><PERSON> distance, and more.

For the Levenshtein distance, you can use the functions WagnerFischer()
and Ukkonen(). Read the documentation on these functions.

For the Jaro and Jaro-Winkler algorithms, check the functions
<PERSON><PERSON>() and <PERSON>aroWinkler(). Read the documentation on these functions.

For the Soundex algorithm, check the function Soundex().

For the Hamming distance algorithm, check the function Hamming().
*/
package smetrics
