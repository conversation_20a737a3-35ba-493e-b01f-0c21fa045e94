name: Release

on:
  push:
    tags:
      - 'v*'

# https://docs.github.com/zh/actions/writing-workflows/choosing-what-your-workflow-does/controlling-permissions-for-github_token
permissions:
  contents: write
  packages: write
  attestations: write
  id-token: write

jobs:
  server-go:
    runs-on: ubuntu-latest
    env:
      SNAPCRAFT_STORE_CREDENTIALS: ${{ secrets.SNAPCRAFT_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: ^1.20
      - uses: docker/setup-qemu-action@68827325e0b33c7199eb31dd4e31fbe9023e06e3 # v2
      - uses: docker/setup-buildx-action@f95db51fddba0c2d1ec667646a06c2ce06100226 # v2
      - name: setup-snapcraft
        # FIXME: the mkdirs are a hack for https://github.com/goreleaser/goreleaser/issues/1715
        run: |
          sudo apt-get update
          sudo apt-get -yq --no-install-suggests --no-install-recommends install snapcraft
          mkdir -p $HOME/.cache/snapcraft/download
          mkdir -p $HOME/.cache/snapcraft/stage-packages
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: openiothub
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@v5
        with:
          version: latest
          args: release --clean
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SNAPCRAFT_STORE_CREDENTIALS: ${{ secrets.SNAPCRAFT_STORE_CREDENTIALS }}