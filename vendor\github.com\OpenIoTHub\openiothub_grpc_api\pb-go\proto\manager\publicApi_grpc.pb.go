// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v5.26.1
// source: publicApi.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PublicApiClient is the client API for PublicApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PublicApiClient interface {
	// 获取网关链接服务器的jwt和手机添加网关的二维码
	GenerateJwtQRCodePair(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*JwtQRCodePair, error)
}

type publicApiClient struct {
	cc grpc.ClientConnInterface
}

func NewPublicApiClient(cc grpc.ClientConnInterface) PublicApiClient {
	return &publicApiClient{cc}
}

func (c *publicApiClient) GenerateJwtQRCodePair(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*JwtQRCodePair, error) {
	out := new(JwtQRCodePair)
	err := c.cc.Invoke(ctx, "/pb.PublicApi/GenerateJwtQRCodePair", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PublicApiServer is the server API for PublicApi service.
// All implementations must embed UnimplementedPublicApiServer
// for forward compatibility
type PublicApiServer interface {
	// 获取网关链接服务器的jwt和手机添加网关的二维码
	GenerateJwtQRCodePair(context.Context, *emptypb.Empty) (*JwtQRCodePair, error)
	mustEmbedUnimplementedPublicApiServer()
}

// UnimplementedPublicApiServer must be embedded to have forward compatible implementations.
type UnimplementedPublicApiServer struct {
}

func (UnimplementedPublicApiServer) GenerateJwtQRCodePair(context.Context, *emptypb.Empty) (*JwtQRCodePair, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateJwtQRCodePair not implemented")
}
func (UnimplementedPublicApiServer) mustEmbedUnimplementedPublicApiServer() {}

// UnsafePublicApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PublicApiServer will
// result in compilation errors.
type UnsafePublicApiServer interface {
	mustEmbedUnimplementedPublicApiServer()
}

func RegisterPublicApiServer(s grpc.ServiceRegistrar, srv PublicApiServer) {
	s.RegisterService(&PublicApi_ServiceDesc, srv)
}

func _PublicApi_GenerateJwtQRCodePair_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PublicApiServer).GenerateJwtQRCodePair(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.PublicApi/GenerateJwtQRCodePair",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PublicApiServer).GenerateJwtQRCodePair(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// PublicApi_ServiceDesc is the grpc.ServiceDesc for PublicApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PublicApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.PublicApi",
	HandlerType: (*PublicApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GenerateJwtQRCodePair",
			Handler:    _PublicApi_GenerateJwtQRCodePair_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "publicApi.proto",
}
