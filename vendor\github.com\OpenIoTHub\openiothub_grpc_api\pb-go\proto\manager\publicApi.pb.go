// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.26.1
// source: publicApi.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type JwtQRCodePair struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GatewayJwt         string `protobuf:"bytes,1,opt,name=GatewayJwt,proto3" json:"GatewayJwt,omitempty"`
	QRCodeForMobileAdd string `protobuf:"bytes,2,opt,name=QRCodeForMobileAdd,proto3" json:"QRCodeForMobileAdd,omitempty"`
}

func (x *JwtQRCodePair) Reset() {
	*x = JwtQRCodePair{}
	if protoimpl.UnsafeEnabled {
		mi := &file_publicApi_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JwtQRCodePair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JwtQRCodePair) ProtoMessage() {}

func (x *JwtQRCodePair) ProtoReflect() protoreflect.Message {
	mi := &file_publicApi_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JwtQRCodePair.ProtoReflect.Descriptor instead.
func (*JwtQRCodePair) Descriptor() ([]byte, []int) {
	return file_publicApi_proto_rawDescGZIP(), []int{0}
}

func (x *JwtQRCodePair) GetGatewayJwt() string {
	if x != nil {
		return x.GatewayJwt
	}
	return ""
}

func (x *JwtQRCodePair) GetQRCodeForMobileAdd() string {
	if x != nil {
		return x.QRCodeForMobileAdd
	}
	return ""
}

var File_publicApi_proto protoreflect.FileDescriptor

var file_publicApi_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x41, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x02, 0x70, 0x62, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x5f, 0x0a, 0x0d, 0x4a, 0x77, 0x74, 0x51, 0x52, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x69,
	0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4a, 0x77, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4a, 0x77,
	0x74, 0x12, 0x2e, 0x0a, 0x12, 0x51, 0x52, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x6f, 0x72, 0x4d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x41, 0x64, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x51,
	0x52, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x6f, 0x72, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x41, 0x64,
	0x64, 0x32, 0x51, 0x0a, 0x09, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x41, 0x70, 0x69, 0x12, 0x44,
	0x0a, 0x15, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4a, 0x77, 0x74, 0x51, 0x52, 0x43,
	0x6f, 0x64, 0x65, 0x50, 0x61, 0x69, 0x72, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a,
	0x11, 0x2e, 0x70, 0x62, 0x2e, 0x4a, 0x77, 0x74, 0x51, 0x52, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61,
	0x69, 0x72, 0x22, 0x00, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x3b, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_publicApi_proto_rawDescOnce sync.Once
	file_publicApi_proto_rawDescData = file_publicApi_proto_rawDesc
)

func file_publicApi_proto_rawDescGZIP() []byte {
	file_publicApi_proto_rawDescOnce.Do(func() {
		file_publicApi_proto_rawDescData = protoimpl.X.CompressGZIP(file_publicApi_proto_rawDescData)
	})
	return file_publicApi_proto_rawDescData
}

var file_publicApi_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_publicApi_proto_goTypes = []interface{}{
	(*JwtQRCodePair)(nil), // 0: pb.JwtQRCodePair
	(*emptypb.Empty)(nil), // 1: google.protobuf.Empty
}
var file_publicApi_proto_depIdxs = []int32{
	1, // 0: pb.PublicApi.GenerateJwtQRCodePair:input_type -> google.protobuf.Empty
	0, // 1: pb.PublicApi.GenerateJwtQRCodePair:output_type -> pb.JwtQRCodePair
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_publicApi_proto_init() }
func file_publicApi_proto_init() {
	if File_publicApi_proto != nil {
		return
	}
	file_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_publicApi_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JwtQRCodePair); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_publicApi_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_publicApi_proto_goTypes,
		DependencyIndexes: file_publicApi_proto_depIdxs,
		MessageInfos:      file_publicApi_proto_msgTypes,
	}.Build()
	File_publicApi_proto = out.File
	file_publicApi_proto_rawDesc = nil
	file_publicApi_proto_goTypes = nil
	file_publicApi_proto_depIdxs = nil
}
