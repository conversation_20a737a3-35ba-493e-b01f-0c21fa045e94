// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v5.26.1
// source: portManager.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PortManagerClient is the client API for PortManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PortManagerClient interface {
	// Port
	GetAllPorts(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*PortInfoList, error)
	AddPort(ctx context.Context, in *PortInfo, opts ...grpc.CallOption) (*OperationResponse, error)
	UpdatePort(ctx context.Context, in *PortInfo, opts ...grpc.CallOption) (*OperationResponse, error)
	DelPort(ctx context.Context, in *PortInfo, opts ...grpc.CallOption) (*OperationResponse, error)
	// server-go服务器查询本服务器所有端口配置的接口
	GetAllPortInfoListByServerUuid(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*PortInfoList, error)
	GetAllHttpInfoListByServerUuid(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*HttpInfoList, error)
}

type portManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewPortManagerClient(cc grpc.ClientConnInterface) PortManagerClient {
	return &portManagerClient{cc}
}

func (c *portManagerClient) GetAllPorts(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*PortInfoList, error) {
	out := new(PortInfoList)
	err := c.cc.Invoke(ctx, "/pb.PortManager/GetAllPorts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *portManagerClient) AddPort(ctx context.Context, in *PortInfo, opts ...grpc.CallOption) (*OperationResponse, error) {
	out := new(OperationResponse)
	err := c.cc.Invoke(ctx, "/pb.PortManager/AddPort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *portManagerClient) UpdatePort(ctx context.Context, in *PortInfo, opts ...grpc.CallOption) (*OperationResponse, error) {
	out := new(OperationResponse)
	err := c.cc.Invoke(ctx, "/pb.PortManager/UpdatePort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *portManagerClient) DelPort(ctx context.Context, in *PortInfo, opts ...grpc.CallOption) (*OperationResponse, error) {
	out := new(OperationResponse)
	err := c.cc.Invoke(ctx, "/pb.PortManager/DelPort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *portManagerClient) GetAllPortInfoListByServerUuid(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*PortInfoList, error) {
	out := new(PortInfoList)
	err := c.cc.Invoke(ctx, "/pb.PortManager/GetAllPortInfoListByServerUuid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *portManagerClient) GetAllHttpInfoListByServerUuid(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*HttpInfoList, error) {
	out := new(HttpInfoList)
	err := c.cc.Invoke(ctx, "/pb.PortManager/GetAllHttpInfoListByServerUuid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PortManagerServer is the server API for PortManager service.
// All implementations must embed UnimplementedPortManagerServer
// for forward compatibility
type PortManagerServer interface {
	// Port
	GetAllPorts(context.Context, *emptypb.Empty) (*PortInfoList, error)
	AddPort(context.Context, *PortInfo) (*OperationResponse, error)
	UpdatePort(context.Context, *PortInfo) (*OperationResponse, error)
	DelPort(context.Context, *PortInfo) (*OperationResponse, error)
	// server-go服务器查询本服务器所有端口配置的接口
	GetAllPortInfoListByServerUuid(context.Context, *emptypb.Empty) (*PortInfoList, error)
	GetAllHttpInfoListByServerUuid(context.Context, *emptypb.Empty) (*HttpInfoList, error)
	mustEmbedUnimplementedPortManagerServer()
}

// UnimplementedPortManagerServer must be embedded to have forward compatible implementations.
type UnimplementedPortManagerServer struct {
}

func (UnimplementedPortManagerServer) GetAllPorts(context.Context, *emptypb.Empty) (*PortInfoList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllPorts not implemented")
}
func (UnimplementedPortManagerServer) AddPort(context.Context, *PortInfo) (*OperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPort not implemented")
}
func (UnimplementedPortManagerServer) UpdatePort(context.Context, *PortInfo) (*OperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePort not implemented")
}
func (UnimplementedPortManagerServer) DelPort(context.Context, *PortInfo) (*OperationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelPort not implemented")
}
func (UnimplementedPortManagerServer) GetAllPortInfoListByServerUuid(context.Context, *emptypb.Empty) (*PortInfoList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllPortInfoListByServerUuid not implemented")
}
func (UnimplementedPortManagerServer) GetAllHttpInfoListByServerUuid(context.Context, *emptypb.Empty) (*HttpInfoList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllHttpInfoListByServerUuid not implemented")
}
func (UnimplementedPortManagerServer) mustEmbedUnimplementedPortManagerServer() {}

// UnsafePortManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PortManagerServer will
// result in compilation errors.
type UnsafePortManagerServer interface {
	mustEmbedUnimplementedPortManagerServer()
}

func RegisterPortManagerServer(s grpc.ServiceRegistrar, srv PortManagerServer) {
	s.RegisterService(&PortManager_ServiceDesc, srv)
}

func _PortManager_GetAllPorts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortManagerServer).GetAllPorts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.PortManager/GetAllPorts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortManagerServer).GetAllPorts(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _PortManager_AddPort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PortInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortManagerServer).AddPort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.PortManager/AddPort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortManagerServer).AddPort(ctx, req.(*PortInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _PortManager_UpdatePort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PortInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortManagerServer).UpdatePort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.PortManager/UpdatePort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortManagerServer).UpdatePort(ctx, req.(*PortInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _PortManager_DelPort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PortInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortManagerServer).DelPort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.PortManager/DelPort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortManagerServer).DelPort(ctx, req.(*PortInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _PortManager_GetAllPortInfoListByServerUuid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortManagerServer).GetAllPortInfoListByServerUuid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.PortManager/GetAllPortInfoListByServerUuid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortManagerServer).GetAllPortInfoListByServerUuid(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _PortManager_GetAllHttpInfoListByServerUuid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortManagerServer).GetAllHttpInfoListByServerUuid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.PortManager/GetAllHttpInfoListByServerUuid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortManagerServer).GetAllHttpInfoListByServerUuid(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// PortManager_ServiceDesc is the grpc.ServiceDesc for PortManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PortManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.PortManager",
	HandlerType: (*PortManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAllPorts",
			Handler:    _PortManager_GetAllPorts_Handler,
		},
		{
			MethodName: "AddPort",
			Handler:    _PortManager_AddPort_Handler,
		},
		{
			MethodName: "UpdatePort",
			Handler:    _PortManager_UpdatePort_Handler,
		},
		{
			MethodName: "DelPort",
			Handler:    _PortManager_DelPort_Handler,
		},
		{
			MethodName: "GetAllPortInfoListByServerUuid",
			Handler:    _PortManager_GetAllPortInfoListByServerUuid_Handler,
		},
		{
			MethodName: "GetAllHttpInfoListByServerUuid",
			Handler:    _PortManager_GetAllHttpInfoListByServerUuid_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "portManager.proto",
}
